{"mappings": ";;;;AAAA;ACAA,YAAY,CAAC;;;;ACCN,MAAM,wCAAE,GAAG,CAAC,AAAC,EAAG,sBAAsB;AACtC,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,sBAAsB;AACtC,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,sBAAsB;AACtC,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,sBAAsB;AACtC,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,OAAO;AACvB,MAAM,wCAAE,GAAG,CAAC,AAAC,EAAG,eAAe;AAC/B,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,4BAA4B;AAC5C,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,+BAA+B;AAC/C,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,kBAAkB;AAClC,MAAM,yCAAE,GAAG,CAAC,AAAC,EAAG,SAAS;AACzB,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,UAAU;AAC1B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,UAAU;AAC1B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,aAAa;AAC7B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,gBAAgB;AAChC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,cAAc;AAC9B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,yBAAyB;AACzC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,SAAS;AACzB,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,cAAc;AAC9B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,eAAe;AAC/B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,sCAAsC;AACtD,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,mBAAmB;AACnC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,kBAAkB;AAClC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,cAAc;AAC9B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,YAAY;AAC5B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,aAAa;AAC7B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,gBAAgB;AAChC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,gBAAgB;AAChC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,gBAAgB;AAChC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,qBAAqB;AACrC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,aAAa;AAC7B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,iBAAiB;AACjC,MAAM,yCAAG,GAAG,EAAE,AAAC,EAAC,oBAAoB;AACpC,MAAM,wCAAE,GAAG,EAAE,AAAC,EAAE,mBAAmB;AAGnC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,sCAAsC;AACtD,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,oBAAoB;AACpC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,+BAA+B;AAC/C,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,kBAAkB;AAClC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,YAAY;AAC5B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,YAAY;AAC5B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,mBAAmB;AACnC,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,aAAa;AAC7B,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,QAAQ;AACxB,MAAM,yCAAE,GAAG,EAAE,AAAC,EAAE,UAAU;;;AC7C1B,MAAM,yCAAM,GAAG,CAAC,AAAC,EAAC,2BAA2B;AAC7C,MAAM,yCAAM,GAAG,CAAC,AAAC,EAAC,6BAA6B;AAC/C,MAAM,yCAAM,GAAG,CAAC,AAAC,EAAC,iDAAiD;AACnE,MAAM,yCAAM,GAAG,CAAC,AAAC,EAAC,uCAAuC;AACzD,MAAM,yCAAM,GAAG,CAAC,AAAC,EAAC,mBAAmB;AAMrC,MAAM,yCAAS,GAAG;IACvB,mQAAmQ;IACnQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC;IACxQ;QAAC,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;QAAE,yCAAM;KAAC,CAAE,KAAK;CAChR,AAAC;;;AHvCF,MAAM,0BAAI,GAAG,eAAM,CAAC,WAAW,4iKAAwD,AAAC;AACxF,MAAM,+BAAS,GAAG,IAAI,kBAAW,CAAC,0BAAI,CAAC,AAAC;AAExC,MAAM,8BAAQ,GAAG,SAAU,CAAC,EAAE;IAC5B,OAAQ,CAAC;QACP,KAAK,yCAAE;YACL,OAAO,yCAAE,CAAC;QAEZ,KAAK,yCAAE,CAAC;QACR,KAAK,yCAAE,CAAC;QACR,KAAK,yCAAE;YACL,OAAO,yCAAE,CAAC;QAEZ,KAAK,yCAAE;YACL,OAAO,wCAAE,CAAC;QAEZ;YACE,OAAO,CAAC,CAAC;KACZ;CACF,AAAC;AAEF,MAAM,8BAAQ,GAAG,SAAU,CAAC,EAAE;IAC5B,OAAQ,CAAC;QACP,KAAK,yCAAE,CAAC;QACR,KAAK,yCAAE;YACL,OAAO,yCAAE,CAAC;QAEZ,KAAK,yCAAE;YACL,OAAO,yCAAE,CAAC;QAEZ;YACE,OAAO,CAAC,CAAC;KACZ;CACF,AAAC;AAEF,MAAM,2BAAK;IACT,YAAY,QAAQ,EAAE,QAAQ,GAAG,KAAK,CAAE;QACtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC1B;CACF;AAED,MAAM,iCAAW;IAYf,aAAa,GAAG;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,AAAC;QAChD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,AAAC;QAE9C,sBAAsB;QACtB,IAAI,AAAC,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAM,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,AAAC,EAAE;YAC5E,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,AAAE,CAAA,IAAI,GAAG,MAAM,CAAA,GAAI,KAAK,GAAK,CAAA,IAAI,GAAG,MAAM,CAAA,GAAI,OAAO,CAAC;SAC9D;QAED,OAAO,IAAI,CAAC;KACb;IAED,aAAa,GAAG;QACd,OAAO,8BAAQ,CAAC,+BAAS,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;KACtD;IAED,cAAc,GAAG;QACf,+CAA+C;QAC/C,OAAQ,IAAI,CAAC,SAAS;YACpB,KAAK,yCAAE;gBACL,OAAO,KAAK,CAAC;YAEf,KAAK,yCAAE,CAAC;YACR,KAAK,yCAAE,CAAC;YACR,KAAK,yCAAE;gBACL,IAAI,CAAC,QAAQ,GAAG,yCAAE,CAAC;gBACnB,OAAO,KAAK,CAAC;YAEf,KAAK,yCAAE;gBACL,IAAI,CAAC,QAAQ,GAAG,yCAAE,CAAC;gBACnB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;KACb;IAED,iBAAiB,CAAC,SAAS,EAAE;QAC3B,6CAA6C;QAC7C,IAAI,WAAW,GAAG,KAAK,AAAC;QACxB,OAAQ,yCAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YAC9C,KAAK,yCAAM;gBACT,WAAW,GAAG,IAAI,CAAC;gBACnB,MAAM;YAER,KAAK,yCAAM;gBACT,WAAW,GAAG,SAAS,KAAK,yCAAE,CAAC;gBAC/B,MAAM;YAER,KAAK,yCAAM;gBACT,WAAW,GAAG,SAAS,KAAK,yCAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,EAAE;oBAChB,WAAW,GAAG,KAAK,CAAC;oBACpB,OAAO,WAAW,CAAC;iBACpB;gBACD,MAAM;YAER,KAAK,yCAAM;gBACT,IAAI,SAAS,KAAK,yCAAE,EAClB,OAAO,WAAW,CAAC;gBAErB,MAAM;YAER,KAAK,yCAAM;gBACT,MAAM;SACT;QAED,IAAI,IAAI,CAAC,IAAI,EACX,WAAW,GAAG,KAAK,CAAC;QAGtB,aAAa;QACb,IAAI,IAAI,CAAC,KAAK,IAAK,CAAA,IAAI,CAAC,QAAQ,KAAK,yCAAE,IAAI,IAAI,CAAC,QAAQ,KAAK,yCAAE,CAAA,AAAC,EAAE;YAChE,WAAW,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB,MACC,IAAI,CAAC,KAAK,GAAI,IAAI,CAAC,QAAQ,KAAK,yCAAE,AAAC,CAAC;QAGtC,aAAa;QACb,IAAI,IAAI,CAAC,QAAQ,KAAK,yCAAE,EAAE;YACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAK,IAAI,CAAC,SAAS,KAAK,yCAAG,EAAE;gBAC9C,WAAW,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aAChB;SACF,MACC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAGjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAE/B,OAAO,WAAW,CAAC;KACpB;IAED,SAAS,GAAG;QACV,6DAA6D;QAC7D,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,AAAC;YACtC,IAAI,CAAC,QAAQ,GAAG,8BAAQ,CAAC,UAAU,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;YAC5B,IAAI,CAAC,IAAI,GAAI,UAAU,KAAK,yCAAI,CAAC;YACjC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAChB;QAED,MAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,AAAC;YACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAEtC,mBAAmB;YACnB,IAAI,AAAC,IAAI,CAAC,QAAQ,KAAK,yCAAE,IAAM,AAAC,IAAI,CAAC,QAAQ,KAAK,yCAAE,IAAM,IAAI,CAAC,SAAS,KAAK,yCAAG,AAAC,EAAE;gBACjF,IAAI,CAAC,QAAQ,GAAG,8BAAQ,CAAC,8BAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACnD,OAAO,IAAI,2BAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aACtC;YAED,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,AAAC;YAExC,IAAI,WAAW,KAAK,IAAI,EACtB,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAGlD,YAAY;YACZ,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,SAAS,KAAK,yCAAG,AAAC,CAAC;YAErC,IAAI,WAAW,EACb,OAAO,IAAI,2BAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAElC;QAED,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAClC,OAAO,IAAI,2BAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACtC;QAED,OAAO,IAAI,CAAC;KACb;IAnJD,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;KAChB;CA2IF;AAED,yBAAc,GAAG,iCAAW,CAAC", "sources": ["src/linebreaker.js", "node_modules/@parcel/node-resolver-core/lib/_empty.js", "src/classes.js", "src/pairs.js"], "sourcesContent": ["import UnicodeTrie from 'unicode-trie';\nimport fs from 'fs';\nimport base64 from 'base64-js';\nimport { BK, CR, LF, NL, SG, WJ, SP, ZWJ, BA, HY, NS, AI, AL, CJ, HL, RI, SA, XX } from './classes';\nimport { DI_BRK, IN_BRK, CI_BRK, CP_BRK, PR_BRK, pairTable } from './pairs';\n\nconst data = base64.toByteArray(fs.readFileSync(__dirname + '/classes.trie', 'base64'));\nconst classTrie = new UnicodeTrie(data);\n\nconst mapClass = function (c) {\n  switch (c) {\n    case AI:\n      return AL;\n\n    case SA:\n    case SG:\n    case XX:\n      return AL;\n\n    case CJ:\n      return NS;\n\n    default:\n      return c;\n  }\n};\n\nconst mapFirst = function (c) {\n  switch (c) {\n    case LF:\n    case NL:\n      return BK;\n\n    case SP:\n      return WJ;\n\n    default:\n      return c;\n  }\n};\n\nclass Break {\n  constructor(position, required = false) {\n    this.position = position;\n    this.required = required;\n  }\n}\n\nclass LineBreaker {\n  constructor(string) {\n    this.string = string;\n    this.pos = 0;\n    this.lastPos = 0;\n    this.curClass = null;\n    this.nextClass = null;\n    this.LB8a = false;\n    this.LB21a = false;\n    this.LB30a = 0;\n  }\n\n  nextCodePoint() {\n    const code = this.string.charCodeAt(this.pos++);\n    const next = this.string.charCodeAt(this.pos);\n\n    // If a surrogate pair\n    if ((0xd800 <= code && code <= 0xdbff) && (0xdc00 <= next && next <= 0xdfff)) {\n      this.pos++;\n      return ((code - 0xd800) * 0x400) + (next - 0xdc00) + 0x10000;\n    }\n\n    return code;\n  }\n\n  nextCharClass() {\n    return mapClass(classTrie.get(this.nextCodePoint()));\n  }\n\n  getSimpleBreak() {\n    // handle classes not handled by the pair table\n    switch (this.nextClass) {\n      case SP:\n        return false;\n\n      case BK:\n      case LF:\n      case NL:\n        this.curClass = BK;\n        return false;\n\n      case CR:\n        this.curClass = CR;\n        return false;\n    }\n\n    return null;\n  }\n\n  getPairTableBreak(lastClass) {\n    // if not handled already, use the pair table\n    let shouldBreak = false;\n    switch (pairTable[this.curClass][this.nextClass]) {\n      case DI_BRK: // Direct break\n        shouldBreak = true;\n        break;\n\n      case IN_BRK: // possible indirect break\n        shouldBreak = lastClass === SP;\n        break;\n\n      case CI_BRK:\n        shouldBreak = lastClass === SP;\n        if (!shouldBreak) {\n          shouldBreak = false;\n          return shouldBreak;\n        }\n        break;\n\n      case CP_BRK: // prohibited for combining marks\n        if (lastClass !== SP) {\n          return shouldBreak;\n        }\n        break;\n\n      case PR_BRK:\n        break;\n    }\n\n    if (this.LB8a) {\n      shouldBreak = false;\n    }\n\n    // Rule LB21a\n    if (this.LB21a && (this.curClass === HY || this.curClass === BA)) {\n      shouldBreak = false;\n      this.LB21a = false;\n    } else {\n      this.LB21a = (this.curClass === HL);\n    }\n\n    // Rule LB30a\n    if (this.curClass === RI) {\n      this.LB30a++;\n      if (this.LB30a == 2 && (this.nextClass === RI)) {\n        shouldBreak = true;\n        this.LB30a = 0;\n      }\n    } else {\n      this.LB30a = 0;\n    }\n\n    this.curClass = this.nextClass;\n\n    return shouldBreak;\n  }\n\n  nextBreak() {\n    // get the first char if we're at the beginning of the string\n    if (this.curClass == null) {\n      let firstClass = this.nextCharClass();\n      this.curClass = mapFirst(firstClass);\n      this.nextClass = firstClass;\n      this.LB8a = (firstClass === ZWJ);\n      this.LB30a = 0;\n    }\n\n    while (this.pos < this.string.length) {\n      this.lastPos = this.pos;\n      const lastClass = this.nextClass;\n      this.nextClass = this.nextCharClass();\n\n      // explicit newline\n      if ((this.curClass === BK) || ((this.curClass === CR) && (this.nextClass !== LF))) {\n        this.curClass = mapFirst(mapClass(this.nextClass));\n        return new Break(this.lastPos, true);\n      }\n\n      let shouldBreak = this.getSimpleBreak();\n\n      if (shouldBreak === null) {\n        shouldBreak = this.getPairTableBreak(lastClass);\n      }\n\n      // Rule LB8a\n      this.LB8a = (this.nextClass === ZWJ);\n\n      if (shouldBreak) {\n        return new Break(this.lastPos);\n      }\n    }\n\n    if (this.lastPos < this.string.length) {\n      this.lastPos = this.string.length;\n      return new Break(this.string.length);\n    }\n\n    return null;\n  }\n}\n\nmodule.exports = LineBreaker;\n", "\"use strict\";", "// The following break classes are handled by the pair table\nexport const OP = 0;   // Opening punctuation\nexport const CL = 1;   // Closing punctuation\nexport const CP = 2;   // Closing parenthesis\nexport const QU = 3;   // Ambiguous quotation\nexport const GL = 4;   // Glue\nexport const NS = 5;   // Non-starters\nexport const EX = 6;   // Exclamation/Interrogation\nexport const SY = 7;   // Symbols allowing break after\nexport const IS = 8;   // Infix separator\nexport const PR = 9;   // Prefix\nexport const PO = 10;  // Postfix\nexport const NU = 11;  // Numeric\nexport const AL = 12;  // Alphabetic\nexport const HL = 13;  // Hebrew Letter\nexport const ID = 14;  // Ideographic\nexport const IN = 15;  // Inseparable characters\nexport const HY = 16;  // Hyphen\nexport const BA = 17;  // Break after\nexport const BB = 18;  // Break before\nexport const B2 = 19;  // Break on either side (but not pair)\nexport const ZW = 20;  // Zero-width space\nexport const CM = 21;  // Combining marks\nexport const WJ = 22;  // Word joiner\nexport const H2 = 23;  // Hangul LV\nexport const H3 = 24;  // Hangul LVT\nexport const JL = 25;  // Hangul L Jamo\nexport const JV = 26;  // Hangul V Jamo\nexport const JT = 27;  // Hangul T Jamo\nexport const RI = 28;  // Regional Indicator\nexport const EB = 29;  // Emoji Base\nexport const EM = 30;  // Emoji Modifier\nexport const ZWJ = 31; // Zero Width Joiner\nexport const CB = 32;  // Contingent break\n\n// The following break classes are not handled by the pair table\nexport const AI = 33;  // Ambiguous (Alphabetic or Ideograph)\nexport const BK = 34;  // Break (mandatory)\nexport const CJ = 35;  // Conditional Japanese Starter\nexport const CR = 36;  // Carriage return\nexport const LF = 37;  // Line feed\nexport const NL = 38;  // Next line\nexport const SA = 39;  // South-East Asian\nexport const SG = 40;  // Surrogates\nexport const SP = 41;  // Space\nexport const XX = 42;  // Unknown\n", "export const DI_BRK = 0; // Direct break opportunity\nexport const IN_BRK = 1; // Indirect break opportunity\nexport const CI_BRK = 2; // Indirect break opportunity for combining marks\nexport const CP_BRK = 3; // Prohibited break for combining marks\nexport const PR_BRK = 4; // Prohibited break\n\n// Based on example pair table from https://www.unicode.org/reports/tr14/tr14-37.html#Table2\n// - ZWJ special processing for LB8a of Revision 41\n// - CB manually added as per Rule LB20\n// - CL, CP, NS, SY, IS, PR, PO, HY, BA, B2 and RI manually adjusted as per LB22 of Revision 45\nexport const pairTable = [\n  //OP   , CL    , CP    , QU    , GL    , NS    , EX    , SY    , IS    , PR    , PO    , NU    , AL    , HL    , ID    , IN    , HY    , BA    , BB    , B2    , ZW    , CM    , WJ    , H2    , H3    , JL    , JV    , JT    , RI    , EB    , EM    , ZWJ   , CB\n  [PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, CP_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK], // OP\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // CL\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // CP\n  [PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK], // QU\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK], // GL\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // NS\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // EX\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // SY\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // IS\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK], // PR\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // PO\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // NU\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // AL\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // HL\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // ID\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // IN\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, DI_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // HY\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, DI_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // BA\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK], // BB\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, PR_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // B2\n  [DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK], // ZW\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // CM\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK], // WJ\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // H2\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // H3\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // JL\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // JV\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // JT\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // RI\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, DI_BRK], // EB\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, IN_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // EM\n  [IN_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, PR_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, IN_BRK, IN_BRK, IN_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK], // ZWJ\n  [DI_BRK, PR_BRK, PR_BRK, IN_BRK, IN_BRK, DI_BRK, PR_BRK, PR_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, PR_BRK, CI_BRK, PR_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, DI_BRK, IN_BRK, DI_BRK]  // CB\n];"], "names": [], "version": 3, "file": "module.mjs.map"}