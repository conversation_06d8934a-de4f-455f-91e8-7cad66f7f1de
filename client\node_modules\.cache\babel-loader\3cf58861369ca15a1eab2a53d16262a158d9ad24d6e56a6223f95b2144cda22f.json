{"ast": null, "code": "/**\n * Vehicle utility functions and data for Siyoga Travel\n * Simple and clean implementation for vehicle selection and cost calculation\n */\n\n// Vehicle categories with pricing and features\nexport const VEHICLE_CATEGORIES = [{\n  id: 'cars',\n  name: 'Cars',\n  type: 'car',\n  passengerCapacity: '1-4 passengers',\n  minPassengers: 1,\n  maxPassengers: 4,\n  driverRate: 110,\n  systemRate: 130,\n  features: ['Fuel efficient', 'Comfortable for small groups', 'Suitable for city travel', 'Air Conditioning'],\n  examples: ['Toyota Corolla, Axio, Prius, Aqua, Vitz', 'Suzuki Alto, Wagon R, Swift, Maruti, Celerio', 'Honda Fit (Jazz), Grace', 'Nissan Leaf, March, Sunny, X-Trail'],\n  description: 'Comfortable sedans suitable for small groups'\n}, {\n  id: 'kdh_flat_roof',\n  name: 'KDH Flat Roof',\n  type: 'van',\n  passengerCapacity: '6-10 passengers',\n  minPassengers: 6,\n  maxPassengers: 10,\n  driverRate: 125,\n  systemRate: 145,\n  features: ['Spacious interior', 'Comfortable for long journeys', 'Ample luggage space', 'Air Conditioning'],\n  examples: ['Toyota HiAce KDH', 'Hyundai H1'],\n  description: 'Spacious vans ideal for medium-sized groups'\n}, {\n  id: 'kdh_high_roof',\n  name: 'KDH High Roof',\n  type: 'van',\n  passengerCapacity: '6-12 passengers',\n  minPassengers: 6,\n  maxPassengers: 12,\n  driverRate: 135,\n  systemRate: 160,\n  features: ['Spacious interior', 'Comfortable for long journeys', 'Ample luggage space', 'Air Conditioning'],\n  examples: ['Toyota HiAce KDH', 'Hyundai H1'],\n  description: 'Spacious vans ideal for medium-sized groups'\n}, {\n  id: 'other_vans',\n  name: 'Other Vans',\n  type: 'van',\n  passengerCapacity: '6-10 passengers',\n  minPassengers: 6,\n  maxPassengers: 10,\n  driverRate: 120,\n  systemRate: 145,\n  features: ['Spacious interior', 'Comfortable for long journeys', 'Ample luggage space', 'Air Conditioning'],\n  examples: ['Toyota Noah, Vellfire, Alphard', 'Suzuki Every', 'Mercedes V-Class, Sprinter', 'Nissan Caravan'],\n  description: 'Spacious vans ideal for medium-sized groups'\n}, {\n  id: 'mini_buses',\n  name: 'Mini Buses',\n  type: 'mini_bus',\n  passengerCapacity: '12-25 passengers',\n  minPassengers: 12,\n  maxPassengers: 25,\n  driverRate: 180,\n  systemRate: 210,\n  features: ['Large seating capacity', 'Comfortable for group travel', 'Spacious luggage area', 'Air Conditioning'],\n  examples: ['Mitsubishi Rosa, Fuso Rosa', 'Toyota Coaster', 'Nissan Civilian', 'Hyundai County'],\n  description: 'Large buses perfect for big groups and long trips'\n}];\n\n// Accommodation costs for drivers (when not provided by tourist)\nexport const ACCOMMODATION_COSTS = {\n  1: 3000,\n  // 1 night\n  2: 5000,\n  // 2 nights  \n  3: 7000 // 3 nights\n};\n\n/**\n * Calculate trip cost based on distance and vehicle selection\n * @param {number} mapDistance - Original distance from Google Maps (km)\n * @param {string} vehicleId - Selected vehicle category ID\n * @param {number} tripDays - Number of days for the trip\n * @param {boolean} accommodationProvided - Whether tourist provides driver accommodation\n * @returns {Object} - Cost breakdown\n */\nexport const calculateTripCost = (mapDistance, vehicleId, tripDays = 1, accommodationProvided = true) => {\n  try {\n    // Find vehicle category\n    const vehicle = VEHICLE_CATEGORIES.find(v => v.id === vehicleId);\n    if (!vehicle) {\n      throw new Error('Vehicle category not found');\n    }\n\n    // Add 10km for practical purposes\n    const practicalDistance = mapDistance + 10;\n\n    // Round up to nearest 10km\n    const roundedDistance = Math.ceil(practicalDistance / 10) * 10;\n\n    // Calculate base trip cost\n    const baseCost = roundedDistance * vehicle.systemRate;\n\n    // Calculate accommodation cost if not provided\n    let accommodationCost = 0;\n    if (!accommodationProvided && tripDays > 1) {\n      const nights = tripDays - 1;\n      accommodationCost = ACCOMMODATION_COSTS[Math.min(nights, 3)] || nights * 2500;\n    }\n\n    // Total cost\n    const totalCost = baseCost + accommodationCost;\n    return {\n      success: true,\n      mapDistance,\n      practicalDistance,\n      roundedDistance,\n      vehicle: vehicle.name,\n      ratePerKm: vehicle.systemRate,\n      baseCost,\n      accommodationCost,\n      totalCost,\n      breakdown: {\n        distance: `${mapDistance} km (map) + 10 km (practical) = ${practicalDistance} km → ${roundedDistance} km (rounded)`,\n        rate: `Rs. ${vehicle.systemRate} per km`,\n        calculation: `${roundedDistance} km × Rs. ${vehicle.systemRate} = Rs. ${baseCost.toLocaleString()}`,\n        accommodation: accommodationProvided ? 'Provided by tourist' : `Rs. ${accommodationCost.toLocaleString()} for ${tripDays - 1} night(s)`,\n        total: `Rs. ${totalCost.toLocaleString()}`\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n/**\n * Get suitable vehicle categories for passenger count\n * @param {number} passengerCount - Number of passengers\n * @returns {Array} - Suitable vehicle categories\n */\nexport const getSuitableVehicles = passengerCount => {\n  return VEHICLE_CATEGORIES.filter(vehicle => passengerCount >= vehicle.minPassengers && passengerCount <= vehicle.maxPassengers);\n};\n\n/**\n * Format currency for display\n * @param {number} amount - Amount to format\n * @returns {string} - Formatted currency string\n */\nexport const formatCurrency = amount => {\n  return `Rs. ${amount.toLocaleString()}`;\n};\nexport default {\n  VEHICLE_CATEGORIES,\n  ACCOMMODATION_COSTS,\n  calculateTripCost,\n  getSuitableVehicles,\n  formatCurrency\n};", "map": {"version": 3, "names": ["VEHICLE_CATEGORIES", "id", "name", "type", "passengerCapacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "driverRate", "systemRate", "features", "examples", "description", "ACCOMMODATION_COSTS", "calculateTripCost", "mapDistance", "vehicleId", "tripDays", "accommodationProvided", "vehicle", "find", "v", "Error", "practicalDistance", "roundedDistance", "Math", "ceil", "baseCost", "accommodationCost", "nights", "min", "totalCost", "success", "ratePerKm", "breakdown", "distance", "rate", "calculation", "toLocaleString", "accommodation", "total", "error", "message", "getSuitableVehicles", "passengerCount", "filter", "formatCurrency", "amount"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/utils/vehicleUtils.js"], "sourcesContent": ["/**\n * Vehicle utility functions and data for Siyoga Travel\n * Simple and clean implementation for vehicle selection and cost calculation\n */\n\n// Vehicle categories with pricing and features\nexport const VEHICLE_CATEGORIES = [\n  {\n    id: 'cars',\n    name: 'Cars',\n    type: 'car',\n    passengerCapacity: '1-4 passengers',\n    minPassengers: 1,\n    maxPassengers: 4,\n    driverRate: 110,\n    systemRate: 130,\n    features: [\n      'Fuel efficient',\n      'Comfortable for small groups', \n      'Suitable for city travel',\n      'Air Conditioning'\n    ],\n    examples: [\n      'Toyota Corolla, Axio, Prius, Aqua, Vitz',\n      'Suzuki Alto, Wagon R, Swift, Maruti, Celerio',\n      'Honda Fit (Jazz), Grace',\n      'Nissan Leaf, March, Sunny, X-Trail'\n    ],\n    description: 'Comfortable sedans suitable for small groups'\n  },\n  {\n    id: 'kdh_flat_roof',\n    name: 'KDH Flat Roof',\n    type: 'van',\n    passengerCapacity: '6-10 passengers',\n    minPassengers: 6,\n    maxPassengers: 10,\n    driverRate: 125,\n    systemRate: 145,\n    features: [\n      'Spacious interior',\n      'Comfortable for long journeys',\n      'Ample luggage space', \n      'Air Conditioning'\n    ],\n    examples: [\n      'Toyota HiAce KDH',\n      'Hyundai H1'\n    ],\n    description: 'Spacious vans ideal for medium-sized groups'\n  },\n  {\n    id: 'kdh_high_roof',\n    name: 'KDH High Roof',\n    type: 'van', \n    passengerCapacity: '6-12 passengers',\n    minPassengers: 6,\n    maxPassengers: 12,\n    driverRate: 135,\n    systemRate: 160,\n    features: [\n      'Spacious interior',\n      'Comfortable for long journeys',\n      'Ample luggage space',\n      'Air Conditioning'\n    ],\n    examples: [\n      'Toyota HiAce KDH',\n      'Hyundai H1'\n    ],\n    description: 'Spacious vans ideal for medium-sized groups'\n  },\n  {\n    id: 'other_vans',\n    name: 'Other Vans',\n    type: 'van',\n    passengerCapacity: '6-10 passengers', \n    minPassengers: 6,\n    maxPassengers: 10,\n    driverRate: 120,\n    systemRate: 145,\n    features: [\n      'Spacious interior',\n      'Comfortable for long journeys',\n      'Ample luggage space',\n      'Air Conditioning'\n    ],\n    examples: [\n      'Toyota Noah, Vellfire, Alphard',\n      'Suzuki Every',\n      'Mercedes V-Class, Sprinter',\n      'Nissan Caravan'\n    ],\n    description: 'Spacious vans ideal for medium-sized groups'\n  },\n  {\n    id: 'mini_buses',\n    name: 'Mini Buses',\n    type: 'mini_bus',\n    passengerCapacity: '12-25 passengers',\n    minPassengers: 12,\n    maxPassengers: 25,\n    driverRate: 180,\n    systemRate: 210,\n    features: [\n      'Large seating capacity',\n      'Comfortable for group travel',\n      'Spacious luggage area',\n      'Air Conditioning'\n    ],\n    examples: [\n      'Mitsubishi Rosa, Fuso Rosa',\n      'Toyota Coaster',\n      'Nissan Civilian',\n      'Hyundai County'\n    ],\n    description: 'Large buses perfect for big groups and long trips'\n  }\n];\n\n// Accommodation costs for drivers (when not provided by tourist)\nexport const ACCOMMODATION_COSTS = {\n  1: 3000, // 1 night\n  2: 5000, // 2 nights  \n  3: 7000  // 3 nights\n};\n\n/**\n * Calculate trip cost based on distance and vehicle selection\n * @param {number} mapDistance - Original distance from Google Maps (km)\n * @param {string} vehicleId - Selected vehicle category ID\n * @param {number} tripDays - Number of days for the trip\n * @param {boolean} accommodationProvided - Whether tourist provides driver accommodation\n * @returns {Object} - Cost breakdown\n */\nexport const calculateTripCost = (mapDistance, vehicleId, tripDays = 1, accommodationProvided = true) => {\n  try {\n    // Find vehicle category\n    const vehicle = VEHICLE_CATEGORIES.find(v => v.id === vehicleId);\n    if (!vehicle) {\n      throw new Error('Vehicle category not found');\n    }\n\n    // Add 10km for practical purposes\n    const practicalDistance = mapDistance + 10;\n    \n    // Round up to nearest 10km\n    const roundedDistance = Math.ceil(practicalDistance / 10) * 10;\n    \n    // Calculate base trip cost\n    const baseCost = roundedDistance * vehicle.systemRate;\n    \n    // Calculate accommodation cost if not provided\n    let accommodationCost = 0;\n    if (!accommodationProvided && tripDays > 1) {\n      const nights = tripDays - 1;\n      accommodationCost = ACCOMMODATION_COSTS[Math.min(nights, 3)] || (nights * 2500);\n    }\n    \n    // Total cost\n    const totalCost = baseCost + accommodationCost;\n    \n    return {\n      success: true,\n      mapDistance,\n      practicalDistance,\n      roundedDistance,\n      vehicle: vehicle.name,\n      ratePerKm: vehicle.systemRate,\n      baseCost,\n      accommodationCost,\n      totalCost,\n      breakdown: {\n        distance: `${mapDistance} km (map) + 10 km (practical) = ${practicalDistance} km → ${roundedDistance} km (rounded)`,\n        rate: `Rs. ${vehicle.systemRate} per km`,\n        calculation: `${roundedDistance} km × Rs. ${vehicle.systemRate} = Rs. ${baseCost.toLocaleString()}`,\n        accommodation: accommodationProvided ? 'Provided by tourist' : `Rs. ${accommodationCost.toLocaleString()} for ${tripDays - 1} night(s)`,\n        total: `Rs. ${totalCost.toLocaleString()}`\n      }\n    };\n    \n  } catch (error) {\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n/**\n * Get suitable vehicle categories for passenger count\n * @param {number} passengerCount - Number of passengers\n * @returns {Array} - Suitable vehicle categories\n */\nexport const getSuitableVehicles = (passengerCount) => {\n  return VEHICLE_CATEGORIES.filter(vehicle => \n    passengerCount >= vehicle.minPassengers && passengerCount <= vehicle.maxPassengers\n  );\n};\n\n/**\n * Format currency for display\n * @param {number} amount - Amount to format\n * @returns {string} - Formatted currency string\n */\nexport const formatCurrency = (amount) => {\n  return `Rs. ${amount.toLocaleString()}`;\n};\n\nexport default {\n  VEHICLE_CATEGORIES,\n  ACCOMMODATION_COSTS,\n  calculateTripCost,\n  getSuitableVehicles,\n  formatCurrency\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,kBAAkB,GAAG,CAChC;EACEC,EAAE,EAAE,MAAM;EACVC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAE,gBAAgB;EACnCC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CACR,gBAAgB,EAChB,8BAA8B,EAC9B,0BAA0B,EAC1B,kBAAkB,CACnB;EACDC,QAAQ,EAAE,CACR,yCAAyC,EACzC,8CAA8C,EAC9C,yBAAyB,EACzB,oCAAoC,CACrC;EACDC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAE,iBAAiB;EACpCC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CACR,mBAAmB,EACnB,+BAA+B,EAC/B,qBAAqB,EACrB,kBAAkB,CACnB;EACDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,YAAY,CACb;EACDC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAE,iBAAiB;EACpCC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CACR,mBAAmB,EACnB,+BAA+B,EAC/B,qBAAqB,EACrB,kBAAkB,CACnB;EACDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,YAAY,CACb;EACDC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,YAAY;EAChBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAE,iBAAiB;EACpCC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CACR,mBAAmB,EACnB,+BAA+B,EAC/B,qBAAqB,EACrB,kBAAkB,CACnB;EACDC,QAAQ,EAAE,CACR,gCAAgC,EAChC,cAAc,EACd,4BAA4B,EAC5B,gBAAgB,CACjB;EACDC,WAAW,EAAE;AACf,CAAC,EACD;EACEV,EAAE,EAAE,YAAY;EAChBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAE,kBAAkB;EACrCC,aAAa,EAAE,EAAE;EACjBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,GAAG;EACfC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,CACR,wBAAwB,EACxB,8BAA8B,EAC9B,uBAAuB,EACvB,kBAAkB,CACnB;EACDC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,CACjB;EACDC,WAAW,EAAE;AACf,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAG;EACjC,CAAC,EAAE,IAAI;EAAE;EACT,CAAC,EAAE,IAAI;EAAE;EACT,CAAC,EAAE,IAAI,CAAE;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,GAAG,CAAC,EAAEC,qBAAqB,GAAG,IAAI,KAAK;EACvG,IAAI;IACF;IACA,MAAMC,OAAO,GAAGlB,kBAAkB,CAACmB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKc,SAAS,CAAC;IAChE,IAAI,CAACG,OAAO,EAAE;MACZ,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;IAC/C;;IAEA;IACA,MAAMC,iBAAiB,GAAGR,WAAW,GAAG,EAAE;;IAE1C;IACA,MAAMS,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,EAAE,CAAC,GAAG,EAAE;;IAE9D;IACA,MAAMI,QAAQ,GAAGH,eAAe,GAAGL,OAAO,CAACV,UAAU;;IAErD;IACA,IAAImB,iBAAiB,GAAG,CAAC;IACzB,IAAI,CAACV,qBAAqB,IAAID,QAAQ,GAAG,CAAC,EAAE;MAC1C,MAAMY,MAAM,GAAGZ,QAAQ,GAAG,CAAC;MAC3BW,iBAAiB,GAAGf,mBAAmB,CAACY,IAAI,CAACK,GAAG,CAACD,MAAM,EAAE,CAAC,CAAC,CAAC,IAAKA,MAAM,GAAG,IAAK;IACjF;;IAEA;IACA,MAAME,SAAS,GAAGJ,QAAQ,GAAGC,iBAAiB;IAE9C,OAAO;MACLI,OAAO,EAAE,IAAI;MACbjB,WAAW;MACXQ,iBAAiB;MACjBC,eAAe;MACfL,OAAO,EAAEA,OAAO,CAAChB,IAAI;MACrB8B,SAAS,EAAEd,OAAO,CAACV,UAAU;MAC7BkB,QAAQ;MACRC,iBAAiB;MACjBG,SAAS;MACTG,SAAS,EAAE;QACTC,QAAQ,EAAE,GAAGpB,WAAW,mCAAmCQ,iBAAiB,SAASC,eAAe,eAAe;QACnHY,IAAI,EAAE,OAAOjB,OAAO,CAACV,UAAU,SAAS;QACxC4B,WAAW,EAAE,GAAGb,eAAe,aAAaL,OAAO,CAACV,UAAU,UAAUkB,QAAQ,CAACW,cAAc,CAAC,CAAC,EAAE;QACnGC,aAAa,EAAErB,qBAAqB,GAAG,qBAAqB,GAAG,OAAOU,iBAAiB,CAACU,cAAc,CAAC,CAAC,QAAQrB,QAAQ,GAAG,CAAC,WAAW;QACvIuB,KAAK,EAAE,OAAOT,SAAS,CAACO,cAAc,CAAC,CAAC;MAC1C;IACF,CAAC;EAEH,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,OAAO;MACLT,OAAO,EAAE,KAAK;MACdS,KAAK,EAAEA,KAAK,CAACC;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAIC,cAAc,IAAK;EACrD,OAAO3C,kBAAkB,CAAC4C,MAAM,CAAC1B,OAAO,IACtCyB,cAAc,IAAIzB,OAAO,CAACb,aAAa,IAAIsC,cAAc,IAAIzB,OAAO,CAACZ,aACvE,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuC,cAAc,GAAIC,MAAM,IAAK;EACxC,OAAO,OAAOA,MAAM,CAACT,cAAc,CAAC,CAAC,EAAE;AACzC,CAAC;AAED,eAAe;EACbrC,kBAAkB;EAClBY,mBAAmB;EACnBC,iBAAiB;EACjB6B,mBAAmB;EACnBG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}