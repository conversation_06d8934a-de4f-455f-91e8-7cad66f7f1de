{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Dashboard() {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const handleLogout = () => {\n    logout();\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 12\n    }, this);\n  }\n  const {\n    user: userData,\n    profile\n  } = user;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"dashboard-title\",\n        children: \"\\uD83C\\uDF0D Siyoga Travel Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleLogout,\n        className: \"logout-btn\",\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Welcome, \", profile.first_name, \" \", profile.last_name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 12\n        }, this), \" \", userData.email]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Role:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 12\n        }, this), \" \", userData.role.charAt(0).toUpperCase() + userData.role.slice(1)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Phone:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 12\n        }, this), \" \", profile.phone]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Account Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: userData.is_verified ? 'green' : 'orange',\n            marginLeft: '5px'\n          },\n          children: userData.is_verified ? 'Verified' : 'Pending Verification'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), userData.role === 'tourist' && profile.country && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Country:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 14\n        }, this), \" \", profile.country]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        color: '#666',\n        marginTop: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDEA7 Coming Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Trip planning, booking management, and more features will be added here!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), userData.role === 'tourist' && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"As a tourist, you'll be able to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            textAlign: 'left',\n            marginTop: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Browse available trips\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Book travel packages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Manage your bookings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Rate and review trips\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this), userData.role === 'driver' && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"As a driver, you'll be able to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            textAlign: 'left',\n            marginTop: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Manage your vehicle information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Accept trip requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Track your earnings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"View customer ratings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_s(Dashboard, \"SlSPRKmTohGnoLiiApupaRii2Oc=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "logout", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userData", "profile", "onClick", "first_name", "last_name", "email", "role", "char<PERSON>t", "toUpperCase", "slice", "phone", "style", "color", "is_verified", "marginLeft", "country", "textAlign", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../context/AuthContext';\n\nfunction Dashboard() {\n  const { user, logout } = useAuth();\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  if (!user) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n\n  const { user: userData, profile } = user;\n\n  return (\n    <div className=\"dashboard\">\n      <div className=\"dashboard-header\">\n        <h1 className=\"dashboard-title\">🌍 Siyoga Travel Dashboard</h1>\n        <button onClick={handleLogout} className=\"logout-btn\">\n          Logout\n        </button>\n      </div>\n\n      <div className=\"user-info\">\n        <h3>Welcome, {profile.first_name} {profile.last_name}!</h3>\n        <p><strong>Email:</strong> {userData.email}</p>\n        <p><strong>Role:</strong> {userData.role.charAt(0).toUpperCase() + userData.role.slice(1)}</p>\n        <p><strong>Phone:</strong> {profile.phone}</p>\n        <p><strong>Account Status:</strong> \n          <span style={{ color: userData.is_verified ? 'green' : 'orange', marginLeft: '5px' }}>\n            {userData.is_verified ? 'Verified' : 'Pending Verification'}\n          </span>\n        </p>\n        {userData.role === 'tourist' && profile.country && (\n          <p><strong>Country:</strong> {profile.country}</p>\n        )}\n      </div>\n\n      <div style={{ textAlign: 'center', color: '#666', marginTop: '2rem' }}>\n        <h3>🚧 Coming Soon</h3>\n        <p>Trip planning, booking management, and more features will be added here!</p>\n        \n        {userData.role === 'tourist' && (\n          <div style={{ marginTop: '1rem' }}>\n            <p>As a tourist, you'll be able to:</p>\n            <ul style={{ textAlign: 'left', marginTop: '0.5rem' }}>\n              <li>Browse available trips</li>\n              <li>Book travel packages</li>\n              <li>Manage your bookings</li>\n              <li>Rate and review trips</li>\n            </ul>\n          </div>\n        )}\n\n        {userData.role === 'driver' && (\n          <div style={{ marginTop: '1rem' }}>\n            <p>As a driver, you'll be able to:</p>\n            <ul style={{ textAlign: 'left', marginTop: '0.5rem' }}>\n              <li>Manage your vehicle information</li>\n              <li>Accept trip requests</li>\n              <li>Track your earnings</li>\n              <li>View customer ratings</li>\n            </ul>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGN,OAAO,CAAC,CAAC;EAElC,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBD,MAAM,CAAC,CAAC;EACV,CAAC;EAED,IAAI,CAACD,IAAI,EAAE;IACT,oBAAOH,OAAA;MAAKM,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,MAAM;IAAER,IAAI,EAAES,QAAQ;IAAEC;EAAQ,CAAC,GAAGV,IAAI;EAExC,oBACEH,OAAA;IAAKM,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBP,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BP,OAAA;QAAIM,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DX,OAAA;QAAQc,OAAO,EAAET,YAAa;QAACC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENX,OAAA;MAAKM,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBP,OAAA;QAAAO,QAAA,GAAI,WAAS,EAACM,OAAO,CAACE,UAAU,EAAC,GAAC,EAACF,OAAO,CAACG,SAAS,EAAC,GAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DX,OAAA;QAAAO,QAAA,gBAAGP,OAAA;UAAAO,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACC,QAAQ,CAACK,KAAK;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CX,OAAA;QAAAO,QAAA,gBAAGP,OAAA;UAAAO,QAAA,EAAQ;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACC,QAAQ,CAACM,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGR,QAAQ,CAACM,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9FX,OAAA;QAAAO,QAAA,gBAAGP,OAAA;UAAAO,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACE,OAAO,CAACS,KAAK;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9CX,OAAA;QAAAO,QAAA,gBAAGP,OAAA;UAAAO,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjCX,OAAA;UAAMuB,KAAK,EAAE;YAAEC,KAAK,EAAEZ,QAAQ,CAACa,WAAW,GAAG,OAAO,GAAG,QAAQ;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAnB,QAAA,EAClFK,QAAQ,CAACa,WAAW,GAAG,UAAU,GAAG;QAAsB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACHC,QAAQ,CAACM,IAAI,KAAK,SAAS,IAAIL,OAAO,CAACc,OAAO,iBAC7C3B,OAAA;QAAAO,QAAA,gBAAGP,OAAA;UAAAO,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACE,OAAO,CAACc,OAAO;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAClD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENX,OAAA;MAAKuB,KAAK,EAAE;QAAEK,SAAS,EAAE,QAAQ;QAAEJ,KAAK,EAAE,MAAM;QAAEK,SAAS,EAAE;MAAO,CAAE;MAAAtB,QAAA,gBACpEP,OAAA;QAAAO,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBX,OAAA;QAAAO,QAAA,EAAG;MAAwE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAE9EC,QAAQ,CAACM,IAAI,KAAK,SAAS,iBAC1BlB,OAAA;QAAKuB,KAAK,EAAE;UAAEM,SAAS,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBAChCP,OAAA;UAAAO,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvCX,OAAA;UAAIuB,KAAK,EAAE;YAAEK,SAAS,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAtB,QAAA,gBACpDP,OAAA;YAAAO,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BX,OAAA;YAAAO,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BX,OAAA;YAAAO,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BX,OAAA;YAAAO,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAEAC,QAAQ,CAACM,IAAI,KAAK,QAAQ,iBACzBlB,OAAA;QAAKuB,KAAK,EAAE;UAAEM,SAAS,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBAChCP,OAAA;UAAAO,QAAA,EAAG;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtCX,OAAA;UAAIuB,KAAK,EAAE;YAAEK,SAAS,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAtB,QAAA,gBACpDP,OAAA;YAAAO,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCX,OAAA;YAAAO,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BX,OAAA;YAAAO,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BX,OAAA;YAAAO,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACT,EAAA,CAnEQD,SAAS;EAAA,QACSH,OAAO;AAAA;AAAAgC,EAAA,GADzB7B,SAAS;AAqElB,eAAeA,SAAS;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}