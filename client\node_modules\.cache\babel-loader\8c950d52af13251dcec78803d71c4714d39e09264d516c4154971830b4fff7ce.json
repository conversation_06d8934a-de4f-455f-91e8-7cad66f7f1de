{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport function useAuth() {\n  _s();\n  return useContext(AuthContext);\n}\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport function AuthProvider({\n  children\n}) {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      // Verify token is still valid\n      checkAuth();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const checkAuth = async () => {\n    try {\n      const response = await axios.get('/api/auth/me');\n      setUser(response.data.data);\n    } catch (error) {\n      // Token is invalid, remove it\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('/api/auth/login', {\n        email,\n        password\n      });\n      const {\n        token,\n        user: userData,\n        profile\n      } = response.data.data;\n\n      // Store token\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      // Set user data\n      setUser({\n        user: userData,\n        profile\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('Registration error:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n\n      // Handle validation errors specifically\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.errors) {\n        const validationErrors = error.response.data.errors.map(err => err.msg).join(', ');\n        return {\n          success: false,\n          message: `Validation failed: ${validationErrors}`,\n          errors: error.response.data.errors\n        };\n      }\n      return {\n        success: false,\n        message: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    setUser(null);\n  };\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n}\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "removeItem", "login", "email", "password", "post", "userData", "profile", "setItem", "success", "_error$response", "_error$response$data", "message", "register", "_error$response2", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "console", "errors", "validationErrors", "map", "err", "msg", "join", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport function useAuth() {\n  return useContext(AuthContext);\n}\n\nexport function AuthProvider({ children }) {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      // Verify token is still valid\n      checkAuth();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await axios.get('/api/auth/me');\n      setUser(response.data.data);\n    } catch (error) {\n      // Token is invalid, remove it\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('/api/auth/login', { email, password });\n      const { token, user: userData, profile } = response.data.data;\n      \n      // Store token\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      \n      // Set user data\n      setUser({ user: userData, profile });\n      \n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        message: error.response?.data?.message || 'Login failed' \n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n      return {\n        success: true,\n        message: response.data.message\n      };\n    } catch (error) {\n      console.error('Registration error:', error.response?.data);\n\n      // Handle validation errors specifically\n      if (error.response?.data?.errors) {\n        const validationErrors = error.response.data.errors.map(err => err.msg).join(', ');\n        return {\n          success: false,\n          message: `Validation failed: ${validationErrors}`,\n          errors: error.response.data.errors\n        };\n      }\n\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Registration failed'\n      };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    setUser(null);\n  };\n\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,SAASQ,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACxB,OAAOR,UAAU,CAACM,WAAW,CAAC;AAChC;AAACE,EAAA,CAFeD,OAAO;AAIvB,OAAO,SAASE,YAAYA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,GAAA;EACzC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;MAClE;MACAM,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,cAAc,CAAC;MAChDX,OAAO,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACAT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChC,OAAOxB,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACrDR,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMpB,KAAK,CAAC4B,IAAI,CAAC,iBAAiB,EAAE;QAAEF,KAAK;QAAEC;MAAS,CAAC,CAAC;MACzE,MAAM;QAAEd,KAAK;QAAEJ,IAAI,EAAEoB,QAAQ;QAAEC;MAAQ,CAAC,GAAGV,QAAQ,CAACE,IAAI,CAACA,IAAI;;MAE7D;MACAR,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAElB,KAAK,CAAC;MACpCb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;;MAElE;MACAH,OAAO,CAAC;QAAED,IAAI,EAAEoB,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MAEpC,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLF,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAF,eAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOP,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMpB,KAAK,CAAC4B,IAAI,CAAC,oBAAoB,EAAEC,QAAQ,CAAC;MACjE,OAAO;QACLG,OAAO,EAAE,IAAI;QACbG,OAAO,EAAEf,QAAQ,CAACE,IAAI,CAACa;MACzB,CAAC;IACH,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdC,OAAO,CAACnB,KAAK,CAAC,qBAAqB,GAAAc,gBAAA,GAAEd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBf,IAAI,CAAC;;MAE1D;MACA,KAAAgB,gBAAA,GAAIf,KAAK,CAACH,QAAQ,cAAAkB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,eAApBA,qBAAA,CAAsBI,MAAM,EAAE;QAChC,MAAMC,gBAAgB,GAAGrB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACqB,MAAM,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAClF,OAAO;UACLhB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,sBAAsBS,gBAAgB,EAAE;UACjDD,MAAM,EAAEpB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACqB;QAC9B,CAAC;MACH;MAEA,OAAO;QACLX,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAK,gBAAA,GAAAjB,KAAK,CAACH,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMc,MAAM,GAAGA,CAAA,KAAM;IACnBnC,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOxB,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDR,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMwC,KAAK,GAAG;IACZzC,IAAI;IACJgB,KAAK;IACLW,QAAQ;IACRa,MAAM;IACNtC;EACF,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACgD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAAC/C,GAAA,CAjGeF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}