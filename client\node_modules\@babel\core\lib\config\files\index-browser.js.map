{"version": 3, "names": ["findConfigUpwards", "rootDir", "findPackageData", "filepath", "directories", "pkg", "isPackage", "findRelativeConfig", "pkgData", "envName", "caller", "config", "ignore", "findRootConfig", "dirname", "loadConfig", "name", "Error", "resolveShowConfigPath", "ROOT_CONFIG_FILENAMES", "exports", "resolvePlugin", "resolvePreset", "loadPlugin", "loadPreset"], "sources": ["../../../src/config/files/index-browser.ts"], "sourcesContent": ["/* c8 ignore start */\n\nimport type { <PERSON><PERSON> } from \"gensync\";\n\nimport type {\n  ConfigFile,\n  IgnoreFile,\n  RelativeConfig,\n  FilePackageData,\n} from \"./types.ts\";\n\nimport type { CallerMetadata } from \"../validation/options.ts\";\n\nexport type { ConfigFile, IgnoreFile, RelativeConfig, FilePackageData };\n\nexport function findConfigUpwards(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  rootDir: string,\n): string | null {\n  return null;\n}\n\n// eslint-disable-next-line require-yield\nexport function* findPackageData(filepath: string): Handler<FilePackageData> {\n  return {\n    filepath,\n    directories: [],\n    pkg: null,\n    isPackage: false,\n  };\n}\n\n// eslint-disable-next-line require-yield\nexport function* findRelativeConfig(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  pkgData: FilePackageData,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<RelativeConfig> {\n  return { config: null, ignore: null };\n}\n\n// eslint-disable-next-line require-yield\nexport function* findRootConfig(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  dirname: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  return null;\n}\n\n// eslint-disable-next-line require-yield\nexport function* loadConfig(\n  name: string,\n  dirname: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile> {\n  throw new Error(`Cannot load ${name} relative to ${dirname} in a browser`);\n}\n\n// eslint-disable-next-line require-yield\nexport function* resolveShowConfigPath(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  dirname: string,\n): Handler<string | null> {\n  return null;\n}\n\nexport const ROOT_CONFIG_FILENAMES: string[] = [];\n\ntype Resolved =\n  | { loader: \"require\"; filepath: string }\n  | { loader: \"import\"; filepath: string };\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function resolvePlugin(name: string, dirname: string): Resolved | null {\n  return null;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function resolvePreset(name: string, dirname: string): Resolved | null {\n  return null;\n}\n\nexport function loadPlugin(\n  name: string,\n  dirname: string,\n): Handler<{\n  filepath: string;\n  value: unknown;\n}> {\n  throw new Error(\n    `Cannot load plugin ${name} relative to ${dirname} in a browser`,\n  );\n}\n\nexport function loadPreset(\n  name: string,\n  dirname: string,\n): Handler<{\n  filepath: string;\n  value: unknown;\n}> {\n  throw new Error(\n    `Cannot load preset ${name} relative to ${dirname} in a browser`,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAeO,SAASA,iBAAiBA,CAE/BC,OAAe,EACA;EACf,OAAO,IAAI;AACb;AAGO,UAAUC,eAAeA,CAACC,QAAgB,EAA4B;EAC3E,OAAO;IACLA,QAAQ;IACRC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE;EACb,CAAC;AACH;AAGO,UAAUC,kBAAkBA,CAEjCC,OAAwB,EAExBC,OAAe,EAEfC,MAAkC,EACT;EACzB,OAAO;IAAEC,MAAM,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC;AACvC;AAGO,UAAUC,cAAcA,CAE7BC,OAAe,EAEfL,OAAe,EAEfC,MAAkC,EACN;EAC5B,OAAO,IAAI;AACb;AAGO,UAAUK,UAAUA,CACzBC,IAAY,EACZF,OAAe,EAEfL,OAAe,EAEfC,MAAkC,EACb;EACrB,MAAM,IAAIO,KAAK,CAAC,eAAeD,IAAI,gBAAgBF,OAAO,eAAe,CAAC;AAC5E;AAGO,UAAUI,qBAAqBA,CAEpCJ,OAAe,EACS;EACxB,OAAO,IAAI;AACb;AAEO,MAAMK,qBAA+B,GAAAC,OAAA,CAAAD,qBAAA,GAAG,EAAE;AAO1C,SAASE,aAAaA,CAACL,IAAY,EAAEF,OAAe,EAAmB;EAC5E,OAAO,IAAI;AACb;AAGO,SAASQ,aAAaA,CAACN,IAAY,EAAEF,OAAe,EAAmB;EAC5E,OAAO,IAAI;AACb;AAEO,SAASS,UAAUA,CACxBP,IAAY,EACZF,OAAe,EAId;EACD,MAAM,IAAIG,KAAK,CACb,sBAAsBD,IAAI,gBAAgBF,OAAO,eACnD,CAAC;AACH;AAEO,SAASU,UAAUA,CACxBR,IAAY,EACZF,OAAe,EAId;EACD,MAAM,IAAIG,KAAK,CACb,sBAAsBD,IAAI,gBAAgBF,OAAO,eACnD,CAAC;AACH;AAAC", "ignoreList": []}