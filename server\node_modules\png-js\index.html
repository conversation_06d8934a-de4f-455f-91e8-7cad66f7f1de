<!DOCTYPE html>
<html>
<body>
<a href="http://github.com/devongovett/png.js"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://a248.e.akamai.net/assets.github.com/img/e6bef7a091f5f3138b8cd40bc3e114258dd68ddf/687474703a2f2f73332e616d617a6f6e6177732e636f6d2f6769746875622f726962626f6e732f666f726b6d655f72696768745f7265645f6161303030302e706e67" alt="Fork me on GitHub"></a>
<select>
    <optgroup label="Static PNGs">
        <option>djay.png</option>
        <option>djay-indexed.png</option>
        <option>laptop.png</option>
        <option>trees.png</option>
    </optgroup>
    <optgroup label="Animated PNGs">
        <option>chompy.png</option>
        <option>spinfox.png</option>
        <option>ball.png</option>
        <option>loading.png</option>
    </optgroup>
</select><br><br>
<canvas></canvas>

<script src="zlib.js"></script>
<script src="png.js"></script>
<script>
    var canvas = document.getElementsByTagName('canvas')[0];
    PNG.load('images/djay.png', canvas);
    
    var select = document.getElementsByTagName('select')[0];
    select.onchange = function() {
        canvas.width = canvas.height = 0;
        PNG.load('images/' + select.options[select.selectedIndex].value, canvas);
    }
</script>
</body>
</html>