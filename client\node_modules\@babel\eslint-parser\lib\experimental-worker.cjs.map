{"version": 3, "names": ["major", "minor", "process", "versions", "node", "split", "map", "Number", "Error", "normalizeESLintConfig", "require", "analyzeScope", "baseParse", "Clients", "client", "WorkerClient", "meta", "exports", "name", "version", "parseForESLint", "code", "options", "normalizedOptions", "ast", "scopeManager", "visitorKeys", "getVisitorKeys"], "sources": ["../src/experimental-worker.cts"], "sourcesContent": ["const [major, minor] = process.versions.node.split(\".\").map(Number);\n\nif (major < 12 || (major === 12 && minor < 3)) {\n  throw new Error(\n    \"@babel/eslint-parser/experimental-worker requires Node.js >= 12.3.0\",\n  );\n}\n\nimport normalizeESLintConfig = require(\"./configuration.cts\");\nimport analyzeScope = require(\"./analyze-scope.cts\");\nimport baseParse = require(\"./parse.cts\");\n\nimport Clients = require(\"./client.cts\");\n\nconst client = new Clients.WorkerClient();\n\nexport const meta = {\n  name: \"@babel/eslint-parser/experimental-worker\",\n  version: PACKAGE_JSON.version,\n};\n\nexport function parseForESLint(code: string, options = {}) {\n  const normalizedOptions = normalizeESLintConfig(options);\n  const ast = baseParse(code, normalizedOptions, client);\n  const scopeManager = analyzeScope(ast, normalizedOptions, client);\n\n  return { ast, scopeManager, visitorKeys: client.getVisitorKeys() };\n}\n"], "mappings": ";;;;;;;AAAA,MAAM,CAACA,KAAK,EAAEC,KAAK,CAAC,GAAGC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;AAEnE,IAAIP,KAAK,GAAG,EAAE,IAAKA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAE,EAAE;EAC7C,MAAM,IAAIO,KAAK,CACb,qEACF,CAAC;AACH;AAAC,MAEMC,qBAAqB,GAAAC,OAAA,CAAW,qBAAqB;AAAA,MACrDC,YAAY,GAAAD,OAAA,CAAW,qBAAqB;AAAA,MAC5CE,SAAS,GAAAF,OAAA,CAAW,aAAa;AAAA,MAEjCG,OAAO,GAAAH,OAAA,CAAW,cAAc;AAEvC,MAAMI,MAAM,GAAG,IAAID,OAAO,CAACE,YAAY,CAAC,CAAC;AAElC,MAAMC,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAG;EAClBE,IAAI,EAAE,0CAA0C;EAChDC,OAAO;AACT,CAAC;AAEM,SAASC,cAAcA,CAACC,IAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzD,MAAMC,iBAAiB,GAAGd,qBAAqB,CAACa,OAAO,CAAC;EACxD,MAAME,GAAG,GAAGZ,SAAS,CAACS,IAAI,EAAEE,iBAAiB,EAAET,MAAM,CAAC;EACtD,MAAMW,YAAY,GAAGd,YAAY,CAACa,GAAG,EAAED,iBAAiB,EAAET,MAAM,CAAC;EAEjE,OAAO;IAAEU,GAAG;IAAEC,YAAY;IAAEC,WAAW,EAAEZ,MAAM,CAACa,cAAc,CAAC;EAAE,CAAC;AACpE", "ignoreList": []}