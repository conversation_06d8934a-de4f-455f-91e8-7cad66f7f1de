{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminDashboard() {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [loading, setLoading] = useState(false);\n  const [data, setData] = useState({\n    stats: null,\n    users: [],\n    drivers: [],\n    bookings: [],\n    logs: []\n  });\n\n  // Redirect if not admin\n  useEffect(() => {\n    if (user && user.user.role !== 'admin') {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  // Load dashboard data\n  useEffect(() => {\n    if (activeTab === 'dashboard') {\n      loadDashboardStats();\n    } else if (activeTab === 'users') {\n      loadUsers();\n    } else if (activeTab === 'drivers') {\n      loadDrivers();\n    } else if (activeTab === 'bookings') {\n      loadBookings();\n    } else if (activeTab === 'logs') {\n      loadLogs();\n    }\n  }, [activeTab]);\n  const loadDashboardStats = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/dashboard/stats');\n      setData(prev => ({\n        ...prev,\n        stats: response.data.data\n      }));\n    } catch (error) {\n      console.error('Failed to load dashboard stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/users');\n      setData(prev => ({\n        ...prev,\n        users: response.data.data\n      }));\n    } catch (error) {\n      console.error('Failed to load users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadDrivers = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/drivers');\n      setData(prev => ({\n        ...prev,\n        drivers: response.data.data\n      }));\n    } catch (error) {\n      console.error('Failed to load drivers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadBookings = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/bookings');\n      setData(prev => ({\n        ...prev,\n        bookings: response.data.data\n      }));\n    } catch (error) {\n      console.error('Failed to load bookings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadLogs = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/logs');\n      setData(prev => ({\n        ...prev,\n        logs: response.data.data\n      }));\n    } catch (error) {\n      console.error('Failed to load admin logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleUserStatus = async userId => {\n    try {\n      await axios.put(`/api/admin/users/${userId}/toggle-status`);\n      loadUsers(); // Reload users\n    } catch (error) {\n      console.error('Failed to toggle user status:', error);\n    }\n  };\n  const updateDriverStatus = async (driverId, status, adminNotes = '') => {\n    try {\n      await axios.put(`/api/admin/drivers/${driverId}/status`, {\n        status,\n        adminNotes\n      });\n      loadDrivers(); // Reload drivers\n    } catch (error) {\n      console.error('Failed to update driver status:', error);\n    }\n  };\n  const downloadReport = async (type, format = 'pdf') => {\n    try {\n      const response = await axios.get(`/api/admin/reports/${type}`, {\n        params: {\n          format\n        },\n        responseType: format === 'pdf' ? 'blob' : 'json'\n      });\n      if (format === 'pdf') {\n        const blob = new Blob([response.data], {\n          type: 'application/pdf'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-report.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    } catch (error) {\n      console.error('Failed to download report:', error);\n    }\n  };\n  const viewReportData = async type => {\n    try {\n      const response = await axios.get(`/api/admin/reports/${type}`);\n      if (response.data.success) {\n        // Create a simple modal or alert to show the data\n        const reportData = response.data.data;\n        let message = `${type.toUpperCase()} REPORT\\n\\n`;\n        if (reportData.summary) {\n          message += 'SUMMARY:\\n';\n          Object.entries(reportData.summary).forEach(([key, value]) => {\n            message += `${key.replace(/_/g, ' ').toUpperCase()}: ${value}\\n`;\n          });\n          message += '\\n';\n        }\n        if (type === 'revenue' && reportData.bookings) {\n          message += `RECENT BOOKINGS (showing first 5):\\n`;\n          reportData.bookings.slice(0, 5).forEach((booking, index) => {\n            message += `${index + 1}. Booking #${booking.booking_id} - Cost: Rs.${booking.total_cost} - Revenue: Rs.${booking.revenue}\\n`;\n          });\n        }\n        alert(message);\n      }\n    } catch (error) {\n      console.error('Failed to view report data:', error);\n      alert('Failed to load report data');\n    }\n  };\n  if (!user || user.user.role !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Access Denied\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1400px',\n        margin: '0 auto',\n        background: 'white',\n        borderRadius: '10px',\n        overflow: 'hidden',\n        boxShadow: '0 10px 25px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#667eea',\n          color: 'white',\n          padding: '20px 30px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              fontSize: '24px'\n            },\n            children: \"\\uD83D\\uDEE1\\uFE0F Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              opacity: 0.9\n            },\n            children: \"Siyoga Travels Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          style: {\n            background: 'rgba(255,255,255,0.2)',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          borderBottom: '1px solid #eee',\n          background: '#f8f9fa'\n        },\n        children: [{\n          key: 'dashboard',\n          label: '📊 Dashboard',\n          icon: '📊'\n        }, {\n          key: 'users',\n          label: '👥 Users',\n          icon: '👥'\n        }, {\n          key: 'drivers',\n          label: '🚗 Drivers',\n          icon: '🚗'\n        }, {\n          key: 'bookings',\n          label: '📋 Bookings',\n          icon: '📋'\n        }, {\n          key: 'reports',\n          label: '📈 Reports',\n          icon: '📈'\n        }, {\n          key: 'logs',\n          label: '🔍 Logs',\n          icon: '🔍'\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            background: activeTab === tab.key ? 'white' : 'transparent',\n            border: 'none',\n            padding: '15px 25px',\n            cursor: 'pointer',\n            borderBottom: activeTab === tab.key ? '3px solid #667eea' : '3px solid transparent',\n            fontWeight: activeTab === tab.key ? 'bold' : 'normal',\n            color: activeTab === tab.key ? '#667eea' : '#666'\n          },\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '30px'\n        },\n        children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '50px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '18px',\n              color: '#666'\n            },\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), activeTab === 'dashboard' && data.stats && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDCCA Dashboard Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n              gap: '20px',\n              marginBottom: '30px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                color: 'white',\n                padding: '25px',\n                borderRadius: '10px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 10px 0',\n                  fontSize: '36px'\n                },\n                children: data.stats.totals.users\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  opacity: 0.9\n                },\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, #f093fb, #f5576c)',\n                color: 'white',\n                padding: '25px',\n                borderRadius: '10px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 10px 0',\n                  fontSize: '36px'\n                },\n                children: data.stats.totals.drivers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  opacity: 0.9\n                },\n                children: \"Total Drivers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, #4facfe, #00f2fe)',\n                color: 'white',\n                padding: '25px',\n                borderRadius: '10px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 10px 0',\n                  fontSize: '36px'\n                },\n                children: data.stats.totals.bookings\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  opacity: 0.9\n                },\n                children: \"Total Bookings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, #43e97b, #38f9d7)',\n                color: 'white',\n                padding: '25px',\n                borderRadius: '10px',\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 10px 0',\n                  fontSize: '36px'\n                },\n                children: [\"LKR \", (data.stats.totals.revenue || 0).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  opacity: 0.9\n                },\n                children: \"Total Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#f8f9fa',\n              padding: '20px',\n              borderRadius: '10px',\n              marginTop: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginTop: 0,\n                color: '#333'\n              },\n              children: \"\\uD83D\\uDCC8 Recent Registrations (Last 30 Days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxHeight: '200px',\n                overflowY: 'auto'\n              },\n              children: data.stats.recentRegistrations.map((reg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  padding: '10px 0',\n                  borderBottom: '1px solid #eee'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: new Date(reg.date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold',\n                    color: '#667eea'\n                  },\n                  children: [reg.count, \" new users\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDC65 User Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    background: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: data.users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: user.user_id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: user.full_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: user.role === 'driver' ? '#e3f2fd' : '#f3e5f5',\n                        color: user.role === 'driver' ? '#1976d2' : '#7b1fa2',\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      },\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: user.is_active ? '#e8f5e8' : '#ffebee',\n                        color: user.is_active ? '#2e7d32' : '#c62828',\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      },\n                      children: user.is_active ? 'Active' : 'Inactive'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleUserStatus(user.user_id),\n                      style: {\n                        background: user.is_active ? '#f44336' : '#4caf50',\n                        color: 'white',\n                        border: 'none',\n                        padding: '6px 12px',\n                        borderRadius: '4px',\n                        cursor: 'pointer',\n                        fontSize: '12px'\n                      },\n                      children: user.is_active ? 'Deactivate' : 'Activate'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this)]\n                }, user.user_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), activeTab === 'drivers' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDE97 Driver Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    background: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Vehicles\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: data.drivers.map(driver => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: driver.driver_id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: [driver.first_name, \" \", driver.last_name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: driver.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: driver.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: driver.status === 'approved' ? '#e8f5e8' : driver.status === 'pending' ? '#fff3e0' : driver.status === 'rejected' ? '#ffebee' : '#f3e5f5',\n                        color: driver.status === 'approved' ? '#2e7d32' : driver.status === 'pending' ? '#f57c00' : driver.status === 'rejected' ? '#c62828' : '#7b1fa2',\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      },\n                      children: driver.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: driver.vehicle_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: driver.status,\n                      onChange: e => updateDriverStatus(driver.driver_id, e.target.value),\n                      style: {\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        border: '1px solid #ddd',\n                        fontSize: '12px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"pending\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 469,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"approved\",\n                        children: \"Approved\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"rejected\",\n                        children: \"Rejected\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 471,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"suspended\",\n                        children: \"Suspended\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 472,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 25\n                  }, this)]\n                }, driver.driver_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), activeTab === 'bookings' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDCCB Booking Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    background: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Tourist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Driver\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Destination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: data.bookings.map(booking => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: booking.booking_id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: booking.tourist_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: booking.driver_name || 'Unassigned'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: booking.destination\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: new Date(booking.start_date).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: booking.status === 'completed' ? '#e8f5e8' : booking.status === 'confirmed' ? '#e3f2fd' : booking.status === 'pending' ? '#fff3e0' : '#ffebee',\n                        color: booking.status === 'completed' ? '#2e7d32' : booking.status === 'confirmed' ? '#1976d2' : booking.status === 'pending' ? '#f57c00' : '#c62828',\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      },\n                      children: booking.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: [\"LKR \", (booking.total_cost || 0).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this)]\n                }, booking.booking_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), activeTab === 'reports' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDCC8 Reports & Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n              gap: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f8f9fa',\n                padding: '25px',\n                borderRadius: '10px',\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  marginTop: 0,\n                  color: '#333'\n                },\n                children: \"\\uD83D\\uDC65 Users Report\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666',\n                  marginBottom: '20px'\n                },\n                children: \"Generate comprehensive reports of all users, tourists, and drivers.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => downloadReport('users', 'pdf'),\n                  style: {\n                    background: '#dc3545',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCC4 Download PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => downloadReport('users', 'json'),\n                  style: {\n                    background: '#28a745',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCCA View Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f8f9fa',\n                padding: '25px',\n                borderRadius: '10px',\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  marginTop: 0,\n                  color: '#333'\n                },\n                children: \"\\uD83D\\uDE97 Driver Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666',\n                  marginBottom: '20px'\n                },\n                children: \"Comprehensive driver statistics, earnings, and performance metrics.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => downloadReport('driver-performance', 'json'),\n                  style: {\n                    background: '#17a2b8',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCCA View Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f8f9fa',\n                padding: '25px',\n                borderRadius: '10px',\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  marginTop: 0,\n                  color: '#333'\n                },\n                children: \"\\uD83D\\uDDFA\\uFE0F Popular Destinations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666',\n                  marginBottom: '20px'\n                },\n                children: \"Most visited destinations, popular routes, and travel patterns.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => downloadReport('popular-destinations', 'json'),\n                  style: {\n                    background: '#fd7e14',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCCA View Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f8f9fa',\n                padding: '25px',\n                borderRadius: '10px',\n                border: '1px solid #e9ecef'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  marginTop: 0,\n                  color: '#333'\n                },\n                children: \"\\uD83D\\uDCB0 Revenue Report\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666',\n                  marginBottom: '20px'\n                },\n                children: \"Total revenue calculation (15% of booking costs) with detailed breakdown.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => downloadReport('revenue'),\n                  style: {\n                    background: '#dc3545',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCC4 Download PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => viewReportData('revenue'),\n                  style: {\n                    background: '#28a745',\n                    color: 'white',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    fontSize: '14px'\n                  },\n                  children: \"\\uD83D\\uDCCA View Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), data.stats && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#e3f2fd',\n              padding: '20px',\n              borderRadius: '10px',\n              marginTop: '30px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginTop: 0,\n                color: '#1976d2'\n              },\n              children: \"\\uD83D\\uDCCA Quick Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: data.stats.totals.users\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"Total Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: data.stats.totals.drivers\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"Active Drivers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: data.stats.totals.bookings\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"Total Bookings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: [\"LKR \", (data.stats.totals.revenue || 0).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"Total Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), activeTab === 'logs' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDD0D Admin Activity Logs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              marginBottom: '20px'\n            },\n            children: \"Track all administrative actions and system activities.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    background: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px',\n                      textAlign: 'left',\n                      border: '1px solid #ddd'\n                    },\n                    children: \"Timestamp\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: data.logs.map(log => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: log.log_id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: log.admin_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: log.action.includes('UPDATE') ? '#e3f2fd' : log.action.includes('VIEW') ? '#f3e5f5' : log.action.includes('SYSTEM') ? '#e8f5e8' : '#fff3e0',\n                        color: log.action.includes('UPDATE') ? '#1976d2' : log.action.includes('VIEW') ? '#7b1fa2' : log.action.includes('SYSTEM') ? '#2e7d32' : '#f57c00',\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      },\n                      children: log.action.replace(/_/g, ' ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 807,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd',\n                      maxWidth: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        whiteSpace: 'nowrap',\n                        fontSize: '12px',\n                        color: '#666'\n                      },\n                      children: log.details ? JSON.stringify(JSON.parse(log.details), null, 2).substring(0, 50) + '...' : 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 824,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '12px',\n                      border: '1px solid #ddd',\n                      fontSize: '12px'\n                    },\n                    children: new Date(log.created_at).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 25\n                  }, this)]\n                }, log.log_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 15\n          }, this), data.logs.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              color: '#666',\n              background: '#f8f9fa',\n              borderRadius: '10px',\n              marginTop: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"No admin activity logs found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminDashboard, \"8QsC/ixl3iL4X6m6KQm1h62vH9U=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "logout", "navigate", "activeTab", "setActiveTab", "loading", "setLoading", "data", "setData", "stats", "users", "drivers", "bookings", "logs", "role", "loadDashboardStats", "loadUsers", "loadDrivers", "loadBookings", "loadLogs", "response", "get", "prev", "error", "console", "toggleUserStatus", "userId", "put", "updateDriverStatus", "driverId", "status", "adminNotes", "downloadReport", "type", "format", "params", "responseType", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "viewReportData", "success", "reportData", "message", "toUpperCase", "summary", "Object", "entries", "for<PERSON>ach", "key", "value", "replace", "slice", "booking", "index", "booking_id", "total_cost", "revenue", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "minHeight", "background", "padding", "max<PERSON><PERSON><PERSON>", "margin", "borderRadius", "overflow", "boxShadow", "color", "display", "justifyContent", "alignItems", "fontSize", "opacity", "onClick", "border", "cursor", "borderBottom", "label", "icon", "map", "tab", "fontWeight", "textAlign", "marginTop", "gridTemplateColumns", "gap", "marginBottom", "totals", "toLocaleString", "maxHeight", "overflowY", "recentRegistrations", "reg", "Date", "date", "toLocaleDateString", "count", "overflowX", "width", "borderCollapse", "user_id", "full_name", "email", "is_active", "driver", "driver_id", "first_name", "last_name", "phone", "vehicle_count", "onChange", "e", "target", "tourist_name", "driver_name", "destination", "start_date", "log", "log_id", "admin_email", "action", "includes", "textOverflow", "whiteSpace", "details", "JSON", "stringify", "parse", "substring", "created_at", "length", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nfunction AdminDashboard() {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [loading, setLoading] = useState(false);\n  const [data, setData] = useState({\n    stats: null,\n    users: [],\n    drivers: [],\n    bookings: [],\n    logs: []\n  });\n\n  // Redirect if not admin\n  useEffect(() => {\n    if (user && user.user.role !== 'admin') {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  // Load dashboard data\n  useEffect(() => {\n    if (activeTab === 'dashboard') {\n      loadDashboardStats();\n    } else if (activeTab === 'users') {\n      loadUsers();\n    } else if (activeTab === 'drivers') {\n      loadDrivers();\n    } else if (activeTab === 'bookings') {\n      loadBookings();\n    } else if (activeTab === 'logs') {\n      loadLogs();\n    }\n  }, [activeTab]);\n\n  const loadDashboardStats = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/dashboard/stats');\n      setData(prev => ({ ...prev, stats: response.data.data }));\n    } catch (error) {\n      console.error('Failed to load dashboard stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/users');\n      setData(prev => ({ ...prev, users: response.data.data }));\n    } catch (error) {\n      console.error('Failed to load users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadDrivers = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/drivers');\n      setData(prev => ({ ...prev, drivers: response.data.data }));\n    } catch (error) {\n      console.error('Failed to load drivers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadBookings = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/bookings');\n      setData(prev => ({ ...prev, bookings: response.data.data }));\n    } catch (error) {\n      console.error('Failed to load bookings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadLogs = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('/api/admin/logs');\n      setData(prev => ({ ...prev, logs: response.data.data }));\n    } catch (error) {\n      console.error('Failed to load admin logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const toggleUserStatus = async (userId) => {\n    try {\n      await axios.put(`/api/admin/users/${userId}/toggle-status`);\n      loadUsers(); // Reload users\n    } catch (error) {\n      console.error('Failed to toggle user status:', error);\n    }\n  };\n\n  const updateDriverStatus = async (driverId, status, adminNotes = '') => {\n    try {\n      await axios.put(`/api/admin/drivers/${driverId}/status`, {\n        status,\n        adminNotes\n      });\n      loadDrivers(); // Reload drivers\n    } catch (error) {\n      console.error('Failed to update driver status:', error);\n    }\n  };\n\n  const downloadReport = async (type, format = 'pdf') => {\n    try {\n      const response = await axios.get(`/api/admin/reports/${type}`, {\n        params: { format },\n        responseType: format === 'pdf' ? 'blob' : 'json'\n      });\n\n      if (format === 'pdf') {\n        const blob = new Blob([response.data], { type: 'application/pdf' });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-report.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    } catch (error) {\n      console.error('Failed to download report:', error);\n    }\n  };\n\n  const viewReportData = async (type) => {\n    try {\n      const response = await axios.get(`/api/admin/reports/${type}`);\n      if (response.data.success) {\n        // Create a simple modal or alert to show the data\n        const reportData = response.data.data;\n        let message = `${type.toUpperCase()} REPORT\\n\\n`;\n\n        if (reportData.summary) {\n          message += 'SUMMARY:\\n';\n          Object.entries(reportData.summary).forEach(([key, value]) => {\n            message += `${key.replace(/_/g, ' ').toUpperCase()}: ${value}\\n`;\n          });\n          message += '\\n';\n        }\n\n        if (type === 'revenue' && reportData.bookings) {\n          message += `RECENT BOOKINGS (showing first 5):\\n`;\n          reportData.bookings.slice(0, 5).forEach((booking, index) => {\n            message += `${index + 1}. Booking #${booking.booking_id} - Cost: Rs.${booking.total_cost} - Revenue: Rs.${booking.revenue}\\n`;\n          });\n        }\n\n        alert(message);\n      }\n    } catch (error) {\n      console.error('Failed to view report data:', error);\n      alert('Failed to load report data');\n    }\n  };\n\n  if (!user || user.user.role !== 'admin') {\n    return <div className=\"loading\">Access Denied</div>;\n  }\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '20px'\n    }}>\n      <div style={{\n        maxWidth: '1400px',\n        margin: '0 auto',\n        background: 'white',\n        borderRadius: '10px',\n        overflow: 'hidden',\n        boxShadow: '0 10px 25px rgba(0,0,0,0.1)'\n      }}>\n        {/* Header */}\n        <div style={{\n          background: '#667eea',\n          color: 'white',\n          padding: '20px 30px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}>\n          <div>\n            <h1 style={{ margin: 0, fontSize: '24px' }}>🛡️ Admin Dashboard</h1>\n            <p style={{ margin: '5px 0 0 0', opacity: 0.9 }}>Siyoga Travels Management</p>\n          </div>\n          <button\n            onClick={logout}\n            style={{\n              background: 'rgba(255,255,255,0.2)',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '5px',\n              cursor: 'pointer'\n            }}\n          >\n            Logout\n          </button>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div style={{\n          display: 'flex',\n          borderBottom: '1px solid #eee',\n          background: '#f8f9fa'\n        }}>\n          {[\n            { key: 'dashboard', label: '📊 Dashboard', icon: '📊' },\n            { key: 'users', label: '👥 Users', icon: '👥' },\n            { key: 'drivers', label: '🚗 Drivers', icon: '🚗' },\n            { key: 'bookings', label: '📋 Bookings', icon: '📋' },\n            { key: 'reports', label: '📈 Reports', icon: '📈' },\n            { key: 'logs', label: '🔍 Logs', icon: '🔍' }\n          ].map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key)}\n              style={{\n                background: activeTab === tab.key ? 'white' : 'transparent',\n                border: 'none',\n                padding: '15px 25px',\n                cursor: 'pointer',\n                borderBottom: activeTab === tab.key ? '3px solid #667eea' : '3px solid transparent',\n                fontWeight: activeTab === tab.key ? 'bold' : 'normal',\n                color: activeTab === tab.key ? '#667eea' : '#666'\n              }}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        {/* Content Area */}\n        <div style={{ padding: '30px' }}>\n          {loading && (\n            <div style={{ textAlign: 'center', padding: '50px' }}>\n              <div style={{ fontSize: '18px', color: '#666' }}>Loading...</div>\n            </div>\n          )}\n\n          {/* Dashboard Tab */}\n          {activeTab === 'dashboard' && data.stats && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>📊 Dashboard Overview</h2>\n              \n              {/* Stats Cards */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '20px',\n                marginBottom: '30px'\n              }}>\n                <div style={{\n                  background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                  color: 'white',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  textAlign: 'center'\n                }}>\n                  <h3 style={{ margin: '0 0 10px 0', fontSize: '36px' }}>{data.stats.totals.users}</h3>\n                  <p style={{ margin: 0, opacity: 0.9 }}>Total Users</p>\n                </div>\n                \n                <div style={{\n                  background: 'linear-gradient(135deg, #f093fb, #f5576c)',\n                  color: 'white',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  textAlign: 'center'\n                }}>\n                  <h3 style={{ margin: '0 0 10px 0', fontSize: '36px' }}>{data.stats.totals.drivers}</h3>\n                  <p style={{ margin: 0, opacity: 0.9 }}>Total Drivers</p>\n                </div>\n                \n                <div style={{\n                  background: 'linear-gradient(135deg, #4facfe, #00f2fe)',\n                  color: 'white',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  textAlign: 'center'\n                }}>\n                  <h3 style={{ margin: '0 0 10px 0', fontSize: '36px' }}>{data.stats.totals.bookings}</h3>\n                  <p style={{ margin: 0, opacity: 0.9 }}>Total Bookings</p>\n                </div>\n                \n                <div style={{\n                  background: 'linear-gradient(135deg, #43e97b, #38f9d7)',\n                  color: 'white',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  textAlign: 'center'\n                }}>\n                  <h3 style={{ margin: '0 0 10px 0', fontSize: '36px' }}>\n                    LKR {(data.stats.totals.revenue || 0).toLocaleString()}\n                  </h3>\n                  <p style={{ margin: 0, opacity: 0.9 }}>Total Revenue</p>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div style={{\n                background: '#f8f9fa',\n                padding: '20px',\n                borderRadius: '10px',\n                marginTop: '20px'\n              }}>\n                <h3 style={{ marginTop: 0, color: '#333' }}>📈 Recent Registrations (Last 30 Days)</h3>\n                <div style={{ maxHeight: '200px', overflowY: 'auto' }}>\n                  {data.stats.recentRegistrations.map((reg, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      padding: '10px 0',\n                      borderBottom: '1px solid #eee'\n                    }}>\n                      <span>{new Date(reg.date).toLocaleDateString()}</span>\n                      <span style={{ fontWeight: 'bold', color: '#667eea' }}>{reg.count} new users</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Users Tab */}\n          {activeTab === 'users' && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>👥 User Management</h2>\n              <div style={{ overflowX: 'auto' }}>\n                <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                  <thead>\n                    <tr style={{ background: '#f8f9fa' }}>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Name</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Email</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Role</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Status</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {data.users.map(user => (\n                      <tr key={user.user_id}>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{user.user_id}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{user.full_name}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{user.email}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <span style={{\n                            background: user.role === 'driver' ? '#e3f2fd' : '#f3e5f5',\n                            color: user.role === 'driver' ? '#1976d2' : '#7b1fa2',\n                            padding: '4px 8px',\n                            borderRadius: '4px',\n                            fontSize: '12px'\n                          }}>\n                            {user.role}\n                          </span>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <span style={{\n                            background: user.is_active ? '#e8f5e8' : '#ffebee',\n                            color: user.is_active ? '#2e7d32' : '#c62828',\n                            padding: '4px 8px',\n                            borderRadius: '4px',\n                            fontSize: '12px'\n                          }}>\n                            {user.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <button\n                            onClick={() => toggleUserStatus(user.user_id)}\n                            style={{\n                              background: user.is_active ? '#f44336' : '#4caf50',\n                              color: 'white',\n                              border: 'none',\n                              padding: '6px 12px',\n                              borderRadius: '4px',\n                              cursor: 'pointer',\n                              fontSize: '12px'\n                            }}\n                          >\n                            {user.is_active ? 'Deactivate' : 'Activate'}\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n\n          {/* Drivers Tab */}\n          {activeTab === 'drivers' && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>🚗 Driver Management</h2>\n              <div style={{ overflowX: 'auto' }}>\n                <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                  <thead>\n                    <tr style={{ background: '#f8f9fa' }}>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Name</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Email</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Phone</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Status</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Vehicles</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {data.drivers.map(driver => (\n                      <tr key={driver.driver_id}>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{driver.driver_id}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          {driver.first_name} {driver.last_name}\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{driver.email}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{driver.phone}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <span style={{\n                            background:\n                              driver.status === 'approved' ? '#e8f5e8' :\n                              driver.status === 'pending' ? '#fff3e0' :\n                              driver.status === 'rejected' ? '#ffebee' : '#f3e5f5',\n                            color:\n                              driver.status === 'approved' ? '#2e7d32' :\n                              driver.status === 'pending' ? '#f57c00' :\n                              driver.status === 'rejected' ? '#c62828' : '#7b1fa2',\n                            padding: '4px 8px',\n                            borderRadius: '4px',\n                            fontSize: '12px'\n                          }}>\n                            {driver.status}\n                          </span>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{driver.vehicle_count}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <select\n                            value={driver.status}\n                            onChange={(e) => updateDriverStatus(driver.driver_id, e.target.value)}\n                            style={{\n                              padding: '4px 8px',\n                              borderRadius: '4px',\n                              border: '1px solid #ddd',\n                              fontSize: '12px'\n                            }}\n                          >\n                            <option value=\"pending\">Pending</option>\n                            <option value=\"approved\">Approved</option>\n                            <option value=\"rejected\">Rejected</option>\n                            <option value=\"suspended\">Suspended</option>\n                          </select>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n\n          {/* Bookings Tab */}\n          {activeTab === 'bookings' && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>📋 Booking Management</h2>\n              <div style={{ overflowX: 'auto' }}>\n                <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                  <thead>\n                    <tr style={{ background: '#f8f9fa' }}>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Tourist</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Driver</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Destination</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Date</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Status</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Cost</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {data.bookings.map(booking => (\n                      <tr key={booking.booking_id}>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{booking.booking_id}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{booking.tourist_name}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          {booking.driver_name || 'Unassigned'}\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{booking.destination}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          {new Date(booking.start_date).toLocaleDateString()}\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <span style={{\n                            background:\n                              booking.status === 'completed' ? '#e8f5e8' :\n                              booking.status === 'confirmed' ? '#e3f2fd' :\n                              booking.status === 'pending' ? '#fff3e0' : '#ffebee',\n                            color:\n                              booking.status === 'completed' ? '#2e7d32' :\n                              booking.status === 'confirmed' ? '#1976d2' :\n                              booking.status === 'pending' ? '#f57c00' : '#c62828',\n                            padding: '4px 8px',\n                            borderRadius: '4px',\n                            fontSize: '12px'\n                          }}>\n                            {booking.status}\n                          </span>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          LKR {(booking.total_cost || 0).toLocaleString()}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n\n          {/* Reports Tab */}\n          {activeTab === 'reports' && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>📈 Reports & Analytics</h2>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '20px'\n              }}>\n                {/* Users Report */}\n                <div style={{\n                  background: '#f8f9fa',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#333' }}>👥 Users Report</h3>\n                  <p style={{ color: '#666', marginBottom: '20px' }}>\n                    Generate comprehensive reports of all users, tourists, and drivers.\n                  </p>\n                  <div style={{ display: 'flex', gap: '10px' }}>\n                    <button\n                      onClick={() => downloadReport('users', 'pdf')}\n                      style={{\n                        background: '#dc3545',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📄 Download PDF\n                    </button>\n                    <button\n                      onClick={() => downloadReport('users', 'json')}\n                      style={{\n                        background: '#28a745',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📊 View Data\n                    </button>\n                  </div>\n                </div>\n\n                {/* Bookings Report */}\n                {/* <div style={{\n                  background: '#f8f9fa',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#333' }}>📋 Bookings Report</h3>\n                  <p style={{ color: '#666', marginBottom: '20px' }}>\n                    Generate detailed booking reports with revenue analytics.\n                  </p>\n                  <div style={{ display: 'flex', gap: '10px' }}>\n                    <button\n                      onClick={() => downloadReport('bookings', 'pdf')}\n                      style={{\n                        background: '#dc3545',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📄 Download PDF\n                    </button>\n                    <button\n                      onClick={() => downloadReport('bookings', 'json')}\n                      style={{\n                        background: '#28a745',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📊 View Data\n                    </button>\n                  </div>\n                </div> */}\n\n                {/* Driver Performance Report */}\n                <div style={{\n                  background: '#f8f9fa',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#333' }}>🚗 Driver Performance</h3>\n                  <p style={{ color: '#666', marginBottom: '20px' }}>\n                    Comprehensive driver statistics, earnings, and performance metrics.\n                  </p>\n                  <div style={{ display: 'flex', gap: '10px' }}>\n                    <button\n                      onClick={() => downloadReport('driver-performance', 'json')}\n                      style={{\n                        background: '#17a2b8',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📊 View Report\n                    </button>\n                  </div>\n                </div>\n\n                {/* Popular Destinations Report */}\n                <div style={{\n                  background: '#f8f9fa',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#333' }}>🗺️ Popular Destinations</h3>\n                  <p style={{ color: '#666', marginBottom: '20px' }}>\n                    Most visited destinations, popular routes, and travel patterns.\n                  </p>\n                  <div style={{ display: 'flex', gap: '10px' }}>\n                    <button\n                      onClick={() => downloadReport('popular-destinations', 'json')}\n                      style={{\n                        background: '#fd7e14',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📊 View Report\n                    </button>\n                  </div>\n                </div>\n\n                {/* Revenue Report */}\n                <div style={{\n                  background: '#f8f9fa',\n                  padding: '25px',\n                  borderRadius: '10px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#333' }}>💰 Revenue Report</h3>\n                  <p style={{ color: '#666', marginBottom: '20px' }}>\n                    Total revenue calculation (15% of booking costs) with detailed breakdown.\n                  </p>\n                  <div style={{ display: 'flex', gap: '10px' }}>\n                    <button\n                      onClick={() => downloadReport('revenue')}\n                      style={{\n                        background: '#dc3545',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📄 Download PDF\n                    </button>\n                    <button\n                      onClick={() => viewReportData('revenue')}\n                      style={{\n                        background: '#28a745',\n                        color: 'white',\n                        border: 'none',\n                        padding: '10px 15px',\n                        borderRadius: '5px',\n                        cursor: 'pointer',\n                        fontSize: '14px'\n                      }}\n                    >\n                      📊 View Data\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Quick Stats */}\n              {data.stats && (\n                <div style={{\n                  background: '#e3f2fd',\n                  padding: '20px',\n                  borderRadius: '10px',\n                  marginTop: '30px'\n                }}>\n                  <h3 style={{ marginTop: 0, color: '#1976d2' }}>📊 Quick Statistics</h3>\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '15px'\n                  }}>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>\n                        {data.stats.totals.users}\n                      </div>\n                      <div style={{ fontSize: '14px', color: '#666' }}>Total Users</div>\n                    </div>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>\n                        {data.stats.totals.drivers}\n                      </div>\n                      <div style={{ fontSize: '14px', color: '#666' }}>Active Drivers</div>\n                    </div>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>\n                        {data.stats.totals.bookings}\n                      </div>\n                      <div style={{ fontSize: '14px', color: '#666' }}>Total Bookings</div>\n                    </div>\n                    <div style={{ textAlign: 'center' }}>\n                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>\n                        LKR {(data.stats.totals.revenue || 0).toLocaleString()}\n                      </div>\n                      <div style={{ fontSize: '14px', color: '#666' }}>Total Revenue</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Admin Logs Tab */}\n          {activeTab === 'logs' && (\n            <div>\n              <h2 style={{ marginTop: 0, color: '#333' }}>🔍 Admin Activity Logs</h2>\n              <p style={{ color: '#666', marginBottom: '20px' }}>\n                Track all administrative actions and system activities.\n              </p>\n\n              <div style={{ overflowX: 'auto' }}>\n                <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                  <thead>\n                    <tr style={{ background: '#f8f9fa' }}>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Admin</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Action</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Details</th>\n                      <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Timestamp</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {data.logs.map(log => (\n                      <tr key={log.log_id}>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{log.log_id}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>{log.admin_email}</td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd' }}>\n                          <span style={{\n                            background:\n                              log.action.includes('UPDATE') ? '#e3f2fd' :\n                              log.action.includes('VIEW') ? '#f3e5f5' :\n                              log.action.includes('SYSTEM') ? '#e8f5e8' : '#fff3e0',\n                            color:\n                              log.action.includes('UPDATE') ? '#1976d2' :\n                              log.action.includes('VIEW') ? '#7b1fa2' :\n                              log.action.includes('SYSTEM') ? '#2e7d32' : '#f57c00',\n                            padding: '4px 8px',\n                            borderRadius: '4px',\n                            fontSize: '12px'\n                          }}>\n                            {log.action.replace(/_/g, ' ')}\n                          </span>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd', maxWidth: '200px' }}>\n                          <div style={{\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            whiteSpace: 'nowrap',\n                            fontSize: '12px',\n                            color: '#666'\n                          }}>\n                            {log.details ? JSON.stringify(JSON.parse(log.details), null, 2).substring(0, 50) + '...' : 'N/A'}\n                          </div>\n                        </td>\n                        <td style={{ padding: '12px', border: '1px solid #ddd', fontSize: '12px' }}>\n                          {new Date(log.created_at).toLocaleString()}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {data.logs.length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  color: '#666',\n                  background: '#f8f9fa',\n                  borderRadius: '10px',\n                  marginTop: '20px'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '10px' }}>📝</div>\n                  <div>No admin activity logs found</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC;IAC/BkB,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACArB,SAAS,CAAC,MAAM;IACd,IAAIQ,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACc,IAAI,KAAK,OAAO,EAAE;MACtCZ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;;EAEpB;EACAV,SAAS,CAAC,MAAM;IACd,IAAIW,SAAS,KAAK,WAAW,EAAE;MAC7BY,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM,IAAIZ,SAAS,KAAK,OAAO,EAAE;MAChCa,SAAS,CAAC,CAAC;IACb,CAAC,MAAM,IAAIb,SAAS,KAAK,SAAS,EAAE;MAClCc,WAAW,CAAC,CAAC;IACf,CAAC,MAAM,IAAId,SAAS,KAAK,UAAU,EAAE;MACnCe,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIf,SAAS,KAAK,MAAM,EAAE;MAC/BgB,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEf,MAAMY,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,4BAA4B,CAAC;MAC9Db,OAAO,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEb,KAAK,EAAEW,QAAQ,CAACb,IAAI,CAACA;MAAK,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,kBAAkB,CAAC;MACpDb,OAAO,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEZ,KAAK,EAAEU,QAAQ,CAACb,IAAI,CAACA;MAAK,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,oBAAoB,CAAC;MACtDb,OAAO,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEX,OAAO,EAAES,QAAQ,CAACb,IAAI,CAACA;MAAK,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,qBAAqB,CAAC;MACvDb,OAAO,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,QAAQ,EAAEQ,QAAQ,CAACb,IAAI,CAACA;MAAK,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3Bb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,iBAAiB,CAAC;MACnDb,OAAO,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,IAAI,EAAEO,QAAQ,CAACb,IAAI,CAACA;MAAK,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAM/B,KAAK,CAACgC,GAAG,CAAC,oBAAoBD,MAAM,gBAAgB,CAAC;MAC3DV,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,GAAG,EAAE,KAAK;IACtE,IAAI;MACF,MAAMpC,KAAK,CAACgC,GAAG,CAAC,sBAAsBE,QAAQ,SAAS,EAAE;QACvDC,MAAM;QACNC;MACF,CAAC,CAAC;MACFd,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMS,cAAc,GAAG,MAAAA,CAAOC,IAAI,EAAEC,MAAM,GAAG,KAAK,KAAK;IACrD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,sBAAsBY,IAAI,EAAE,EAAE;QAC7DE,MAAM,EAAE;UAAED;QAAO,CAAC;QAClBE,YAAY,EAAEF,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG;MAC5C,CAAC,CAAC;MAEF,IAAIA,MAAM,KAAK,KAAK,EAAE;QACpB,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAClB,QAAQ,CAACb,IAAI,CAAC,EAAE;UAAE0B,IAAI,EAAE;QAAkB,CAAC,CAAC;QACnE,MAAMM,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QACfI,IAAI,CAACI,QAAQ,GAAG,GAAGd,IAAI,aAAa;QACpCW,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;MACjC;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAM8B,cAAc,GAAG,MAAOpB,IAAI,IAAK;IACrC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,sBAAsBY,IAAI,EAAE,CAAC;MAC9D,IAAIb,QAAQ,CAACb,IAAI,CAAC+C,OAAO,EAAE;QACzB;QACA,MAAMC,UAAU,GAAGnC,QAAQ,CAACb,IAAI,CAACA,IAAI;QACrC,IAAIiD,OAAO,GAAG,GAAGvB,IAAI,CAACwB,WAAW,CAAC,CAAC,aAAa;QAEhD,IAAIF,UAAU,CAACG,OAAO,EAAE;UACtBF,OAAO,IAAI,YAAY;UACvBG,MAAM,CAACC,OAAO,CAACL,UAAU,CAACG,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;YAC3DP,OAAO,IAAI,GAAGM,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACP,WAAW,CAAC,CAAC,KAAKM,KAAK,IAAI;UAClE,CAAC,CAAC;UACFP,OAAO,IAAI,IAAI;QACjB;QAEA,IAAIvB,IAAI,KAAK,SAAS,IAAIsB,UAAU,CAAC3C,QAAQ,EAAE;UAC7C4C,OAAO,IAAI,sCAAsC;UACjDD,UAAU,CAAC3C,QAAQ,CAACqD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,OAAO,CAAC,CAACK,OAAO,EAAEC,KAAK,KAAK;YAC1DX,OAAO,IAAI,GAAGW,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACE,UAAU,eAAeF,OAAO,CAACG,UAAU,kBAAkBH,OAAO,CAACI,OAAO,IAAI;UAC/H,CAAC,CAAC;QACJ;QAEAC,KAAK,CAACf,OAAO,CAAC;MAChB;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDgD,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;EAED,IAAI,CAACvE,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACc,IAAI,KAAK,OAAO,EAAE;IACvC,oBAAOjB,OAAA;MAAK2E,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrD;EAEA,oBACEhF,OAAA;IAAKiF,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE;IACX,CAAE;IAAAR,QAAA,eACA5E,OAAA;MAAKiF,KAAK,EAAE;QACVI,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,QAAQ;QAChBH,UAAU,EAAE,OAAO;QACnBI,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE;MACb,CAAE;MAAAb,QAAA,gBAEA5E,OAAA;QAAKiF,KAAK,EAAE;UACVE,UAAU,EAAE,SAAS;UACrBO,KAAK,EAAE,OAAO;UACdN,OAAO,EAAE,WAAW;UACpBO,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE;QACd,CAAE;QAAAjB,QAAA,gBACA5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEK,MAAM,EAAE,CAAC;cAAEQ,QAAQ,EAAE;YAAO,CAAE;YAAAlB,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEhF,OAAA;YAAGiF,KAAK,EAAE;cAAEK,MAAM,EAAE,WAAW;cAAES,OAAO,EAAE;YAAI,CAAE;YAAAnB,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNhF,OAAA;UACEgG,OAAO,EAAE5F,MAAO;UAChB6E,KAAK,EAAE;YACLE,UAAU,EAAE,uBAAuB;YACnCO,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdb,OAAO,EAAE,WAAW;YACpBG,YAAY,EAAE,KAAK;YACnBW,MAAM,EAAE;UACV,CAAE;UAAAtB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhF,OAAA;QAAKiF,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfQ,YAAY,EAAE,gBAAgB;UAC9BhB,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,EACC,CACC;UAAEX,GAAG,EAAE,WAAW;UAAEmC,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE;QAAK,CAAC,EACvD;UAAEpC,GAAG,EAAE,OAAO;UAAEmC,KAAK,EAAE,UAAU;UAAEC,IAAI,EAAE;QAAK,CAAC,EAC/C;UAAEpC,GAAG,EAAE,SAAS;UAAEmC,KAAK,EAAE,YAAY;UAAEC,IAAI,EAAE;QAAK,CAAC,EACnD;UAAEpC,GAAG,EAAE,UAAU;UAAEmC,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE;QAAK,CAAC,EACrD;UAAEpC,GAAG,EAAE,SAAS;UAAEmC,KAAK,EAAE,YAAY;UAAEC,IAAI,EAAE;QAAK,CAAC,EACnD;UAAEpC,GAAG,EAAE,MAAM;UAAEmC,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAK,CAAC,CAC9C,CAACC,GAAG,CAACC,GAAG,iBACPvG,OAAA;UAEEgG,OAAO,EAAEA,CAAA,KAAMzF,YAAY,CAACgG,GAAG,CAACtC,GAAG,CAAE;UACrCgB,KAAK,EAAE;YACLE,UAAU,EAAE7E,SAAS,KAAKiG,GAAG,CAACtC,GAAG,GAAG,OAAO,GAAG,aAAa;YAC3DgC,MAAM,EAAE,MAAM;YACdb,OAAO,EAAE,WAAW;YACpBc,MAAM,EAAE,SAAS;YACjBC,YAAY,EAAE7F,SAAS,KAAKiG,GAAG,CAACtC,GAAG,GAAG,mBAAmB,GAAG,uBAAuB;YACnFuC,UAAU,EAAElG,SAAS,KAAKiG,GAAG,CAACtC,GAAG,GAAG,MAAM,GAAG,QAAQ;YACrDyB,KAAK,EAAEpF,SAAS,KAAKiG,GAAG,CAACtC,GAAG,GAAG,SAAS,GAAG;UAC7C,CAAE;UAAAW,QAAA,EAED2B,GAAG,CAACH;QAAK,GAZLG,GAAG,CAACtC,GAAG;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhF,OAAA;QAAKiF,KAAK,EAAE;UAAEG,OAAO,EAAE;QAAO,CAAE;QAAAR,QAAA,GAC7BpE,OAAO,iBACNR,OAAA;UAAKiF,KAAK,EAAE;YAAEwB,SAAS,EAAE,QAAQ;YAAErB,OAAO,EAAE;UAAO,CAAE;UAAAR,QAAA,eACnD5E,OAAA;YAAKiF,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEJ,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN,EAGA1E,SAAS,KAAK,WAAW,IAAII,IAAI,CAACE,KAAK,iBACtCZ,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGtEhF,OAAA;YAAKiF,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfgB,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE,MAAM;cACXC,YAAY,EAAE;YAChB,CAAE;YAAAjC,QAAA,gBACA5E,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,2CAA2C;gBACvDO,KAAK,EAAE,OAAO;gBACdN,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBkB,SAAS,EAAE;cACb,CAAE;cAAA7B,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,YAAY;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAAElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAACjG;cAAK;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrFhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAENhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,2CAA2C;gBACvDO,KAAK,EAAE,OAAO;gBACdN,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBkB,SAAS,EAAE;cACb,CAAE;cAAA7B,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,YAAY;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAAElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAAChG;cAAO;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvFhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eAENhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,2CAA2C;gBACvDO,KAAK,EAAE,OAAO;gBACdN,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBkB,SAAS,EAAE;cACb,CAAE;cAAA7B,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,YAAY;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAAElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAAC/F;cAAQ;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxFhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAENhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,2CAA2C;gBACvDO,KAAK,EAAE,OAAO;gBACdN,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBkB,SAAS,EAAE;cACb,CAAE;cAAA7B,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,YAAY;kBAAEQ,QAAQ,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,GAAC,MACjD,EAAC,CAAClE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAACrC,OAAO,IAAI,CAAC,EAAEsC,cAAc,CAAC,CAAC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACLhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAEK,MAAM,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAKiF,KAAK,EAAE;cACVE,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfG,YAAY,EAAE,MAAM;cACpBmB,SAAS,EAAE;YACb,CAAE;YAAA9B,QAAA,gBACA5E,OAAA;cAAIiF,KAAK,EAAE;gBAAEyB,SAAS,EAAE,CAAC;gBAAEhB,KAAK,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvFhF,OAAA;cAAKiF,KAAK,EAAE;gBAAE+B,SAAS,EAAE,OAAO;gBAAEC,SAAS,EAAE;cAAO,CAAE;cAAArC,QAAA,EACnDlE,IAAI,CAACE,KAAK,CAACsG,mBAAmB,CAACZ,GAAG,CAAC,CAACa,GAAG,EAAE7C,KAAK,kBAC7CtE,OAAA;gBAAiBiF,KAAK,EAAE;kBACtBU,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BR,OAAO,EAAE,QAAQ;kBACjBe,YAAY,EAAE;gBAChB,CAAE;gBAAAvB,QAAA,gBACA5E,OAAA;kBAAA4E,QAAA,EAAO,IAAIwC,IAAI,CAACD,GAAG,CAACE,IAAI,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDhF,OAAA;kBAAMiF,KAAK,EAAE;oBAAEuB,UAAU,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAU,CAAE;kBAAAd,QAAA,GAAEuC,GAAG,CAACI,KAAK,EAAC,YAAU;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAP3EV,KAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1E,SAAS,KAAK,OAAO,iBACpBN,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEhF,OAAA;YAAKiF,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAA5C,QAAA,eAChC5E,OAAA;cAAOiF,KAAK,EAAE;gBAAEwC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAA9C,QAAA,gBAC1D5E,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBAAIiF,KAAK,EAAE;oBAAEE,UAAU,EAAE;kBAAU,CAAE;kBAAAP,QAAA,gBACnC5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhF,OAAA;gBAAA4E,QAAA,EACGlE,IAAI,CAACG,KAAK,CAACyF,GAAG,CAACnG,IAAI,iBAClBH,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEzE,IAAI,CAACwH;kBAAO;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEzE,IAAI,CAACyH;kBAAS;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEzE,IAAI,CAAC0H;kBAAK;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBAAMiF,KAAK,EAAE;wBACXE,UAAU,EAAEhF,IAAI,CAACc,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;wBAC1DyE,KAAK,EAAEvF,IAAI,CAACc,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;wBACrDmE,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBO,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EACCzE,IAAI,CAACc;oBAAI;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBAAMiF,KAAK,EAAE;wBACXE,UAAU,EAAEhF,IAAI,CAAC2H,SAAS,GAAG,SAAS,GAAG,SAAS;wBAClDpC,KAAK,EAAEvF,IAAI,CAAC2H,SAAS,GAAG,SAAS,GAAG,SAAS;wBAC7C1C,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBO,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EACCzE,IAAI,CAAC2H,SAAS,GAAG,QAAQ,GAAG;oBAAU;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMpE,gBAAgB,CAACzB,IAAI,CAACwH,OAAO,CAAE;sBAC9C1C,KAAK,EAAE;wBACLE,UAAU,EAAEhF,IAAI,CAAC2H,SAAS,GAAG,SAAS,GAAG,SAAS;wBAClDpC,KAAK,EAAE,OAAO;wBACdO,MAAM,EAAE,MAAM;wBACdb,OAAO,EAAE,UAAU;wBACnBG,YAAY,EAAE,KAAK;wBACnBW,MAAM,EAAE,SAAS;wBACjBJ,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EAEDzE,IAAI,CAAC2H,SAAS,GAAG,YAAY,GAAG;oBAAU;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAzCE7E,IAAI,CAACwH,OAAO;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0CjB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1E,SAAS,KAAK,SAAS,iBACtBN,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEhF,OAAA;YAAKiF,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAA5C,QAAA,eAChC5E,OAAA;cAAOiF,KAAK,EAAE;gBAAEwC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAA9C,QAAA,gBAC1D5E,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBAAIiF,KAAK,EAAE;oBAAEE,UAAU,EAAE;kBAAU,CAAE;kBAAAP,QAAA,gBACnC5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1FhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhF,OAAA;gBAAA4E,QAAA,EACGlE,IAAI,CAACI,OAAO,CAACwF,GAAG,CAACyB,MAAM,iBACtB/H,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEmD,MAAM,CAACC;kBAAS;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,GACtDmD,MAAM,CAACE,UAAU,EAAC,GAAC,EAACF,MAAM,CAACG,SAAS;kBAAA;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEmD,MAAM,CAACF;kBAAK;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEmD,MAAM,CAACI;kBAAK;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBAAMiF,KAAK,EAAE;wBACXE,UAAU,EACR4C,MAAM,CAAC9F,MAAM,KAAK,UAAU,GAAG,SAAS,GACxC8F,MAAM,CAAC9F,MAAM,KAAK,SAAS,GAAG,SAAS,GACvC8F,MAAM,CAAC9F,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;wBACtDyD,KAAK,EACHqC,MAAM,CAAC9F,MAAM,KAAK,UAAU,GAAG,SAAS,GACxC8F,MAAM,CAAC9F,MAAM,KAAK,SAAS,GAAG,SAAS,GACvC8F,MAAM,CAAC9F,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;wBACtDmD,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBO,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EACCmD,MAAM,CAAC9F;oBAAM;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEmD,MAAM,CAACK;kBAAa;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBACEkE,KAAK,EAAE6D,MAAM,CAAC9F,MAAO;sBACrBoG,QAAQ,EAAGC,CAAC,IAAKvG,kBAAkB,CAACgG,MAAM,CAACC,SAAS,EAAEM,CAAC,CAACC,MAAM,CAACrE,KAAK,CAAE;sBACtEe,KAAK,EAAE;wBACLG,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBU,MAAM,EAAE,gBAAgB;wBACxBH,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,gBAEF5E,OAAA;wBAAQkE,KAAK,EAAC,SAAS;wBAAAU,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACxChF,OAAA;wBAAQkE,KAAK,EAAC,UAAU;wBAAAU,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1ChF,OAAA;wBAAQkE,KAAK,EAAC,UAAU;wBAAAU,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1ChF,OAAA;wBAAQkE,KAAK,EAAC,WAAW;wBAAAU,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAzCE+C,MAAM,CAACC,SAAS;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0CrB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1E,SAAS,KAAK,UAAU,iBACvBN,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEhF,OAAA;YAAKiF,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAA5C,QAAA,eAChC5E,OAAA;cAAOiF,KAAK,EAAE;gBAAEwC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAA9C,QAAA,gBAC1D5E,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBAAIiF,KAAK,EAAE;oBAAEE,UAAU,EAAE;kBAAU,CAAE;kBAAAP,QAAA,gBACnC5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7FhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhF,OAAA;gBAAA4E,QAAA,EACGlE,IAAI,CAACK,QAAQ,CAACuF,GAAG,CAACjC,OAAO,iBACxBrE,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEP,OAAO,CAACE;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEP,OAAO,CAACmE;kBAAY;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EACtDP,OAAO,CAACoE,WAAW,IAAI;kBAAY;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEP,OAAO,CAACqE;kBAAW;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EACtD,IAAIwC,IAAI,CAAC/C,OAAO,CAACsE,UAAU,CAAC,CAACrB,kBAAkB,CAAC;kBAAC;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBAAMiF,KAAK,EAAE;wBACXE,UAAU,EACRd,OAAO,CAACpC,MAAM,KAAK,WAAW,GAAG,SAAS,GAC1CoC,OAAO,CAACpC,MAAM,KAAK,WAAW,GAAG,SAAS,GAC1CoC,OAAO,CAACpC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;wBACtDyD,KAAK,EACHrB,OAAO,CAACpC,MAAM,KAAK,WAAW,GAAG,SAAS,GAC1CoC,OAAO,CAACpC,MAAM,KAAK,WAAW,GAAG,SAAS,GAC1CoC,OAAO,CAACpC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;wBACtDmD,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBO,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EACCP,OAAO,CAACpC;oBAAM;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,GAAC,MACpD,EAAC,CAACP,OAAO,CAACG,UAAU,IAAI,CAAC,EAAEuC,cAAc,CAAC,CAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA,GA7BEX,OAAO,CAACE,UAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BvB,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1E,SAAS,KAAK,SAAS,iBACtBN,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEvEhF,OAAA;YAAKiF,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfgB,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAAhC,QAAA,gBAEA5E,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBU,MAAM,EAAE;cACV,CAAE;cAAArB,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEyB,SAAS,EAAE,CAAC;kBAAEhB,KAAK,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAES,KAAK,EAAE,MAAM;kBAAEmB,YAAY,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,GAAG,EAAE;gBAAO,CAAE;gBAAAhC,QAAA,gBAC3C5E,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,OAAO,EAAE,KAAK,CAAE;kBAC9C8C,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThF,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,OAAO,EAAE,MAAM,CAAE;kBAC/C8C,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eA8CNhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBU,MAAM,EAAE;cACV,CAAE;cAAArB,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEyB,SAAS,EAAE,CAAC;kBAAEhB,KAAK,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAES,KAAK,EAAE,MAAM;kBAAEmB,YAAY,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,GAAG,EAAE;gBAAO,CAAE;gBAAAhC,QAAA,eAC3C5E,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,oBAAoB,EAAE,MAAM,CAAE;kBAC5D8C,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBU,MAAM,EAAE;cACV,CAAE;cAAArB,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEyB,SAAS,EAAE,CAAC;kBAAEhB,KAAK,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAES,KAAK,EAAE,MAAM;kBAAEmB,YAAY,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,GAAG,EAAE;gBAAO,CAAE;gBAAAhC,QAAA,eAC3C5E,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,sBAAsB,EAAE,MAAM,CAAE;kBAC9D8C,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhF,OAAA;cAAKiF,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,MAAM;gBACfG,YAAY,EAAE,MAAM;gBACpBU,MAAM,EAAE;cACV,CAAE;cAAArB,QAAA,gBACA5E,OAAA;gBAAIiF,KAAK,EAAE;kBAAEyB,SAAS,EAAE,CAAC;kBAAEhB,KAAK,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEhF,OAAA;gBAAGiF,KAAK,EAAE;kBAAES,KAAK,EAAE,MAAM;kBAAEmB,YAAY,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEiB,GAAG,EAAE;gBAAO,CAAE;gBAAAhC,QAAA,gBAC3C5E,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,SAAS,CAAE;kBACzC8C,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThF,OAAA;kBACEgG,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAAC,SAAS,CAAE;kBACzCyB,KAAK,EAAE;oBACLE,UAAU,EAAE,SAAS;oBACrBO,KAAK,EAAE,OAAO;oBACdO,MAAM,EAAE,MAAM;oBACdb,OAAO,EAAE,WAAW;oBACpBG,YAAY,EAAE,KAAK;oBACnBW,MAAM,EAAE,SAAS;oBACjBJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlB,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLtE,IAAI,CAACE,KAAK,iBACTZ,OAAA;YAAKiF,KAAK,EAAE;cACVE,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfG,YAAY,EAAE,MAAM;cACpBmB,SAAS,EAAE;YACb,CAAE;YAAA9B,QAAA,gBACA5E,OAAA;cAAIiF,KAAK,EAAE;gBAAEyB,SAAS,EAAE,CAAC;gBAAEhB,KAAK,EAAE;cAAU,CAAE;cAAAd,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEhF,OAAA;cAAKiF,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfgB,mBAAmB,EAAE,sCAAsC;gBAC3DC,GAAG,EAAE;cACP,CAAE;cAAAhC,QAAA,gBACA5E,OAAA;gBAAKiF,KAAK,EAAE;kBAAEwB,SAAS,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBAClC5E,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEU,UAAU,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAU,CAAE;kBAAAd,QAAA,EACpElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAACjG;gBAAK;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACNhF,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEJ,KAAK,EAAE;kBAAO,CAAE;kBAAAd,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEwB,SAAS,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBAClC5E,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEU,UAAU,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAU,CAAE;kBAAAd,QAAA,EACpElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAAChG;gBAAO;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNhF,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEJ,KAAK,EAAE;kBAAO,CAAE;kBAAAd,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEwB,SAAS,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBAClC5E,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEU,UAAU,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAU,CAAE;kBAAAd,QAAA,EACpElE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAAC/F;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACNhF,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEJ,KAAK,EAAE;kBAAO,CAAE;kBAAAd,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNhF,OAAA;gBAAKiF,KAAK,EAAE;kBAAEwB,SAAS,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBAClC5E,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEU,UAAU,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAU,CAAE;kBAAAd,QAAA,GAAC,MAClE,EAAC,CAAClE,IAAI,CAACE,KAAK,CAACkG,MAAM,CAACrC,OAAO,IAAI,CAAC,EAAEsC,cAAc,CAAC,CAAC;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACNhF,OAAA;kBAAKiF,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEJ,KAAK,EAAE;kBAAO,CAAE;kBAAAd,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGA1E,SAAS,KAAK,MAAM,iBACnBN,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAIiF,KAAK,EAAE;cAAEyB,SAAS,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEhF,OAAA;YAAGiF,KAAK,EAAE;cAAES,KAAK,EAAE,MAAM;cAAEmB,YAAY,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJhF,OAAA;YAAKiF,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAA5C,QAAA,eAChC5E,OAAA;cAAOiF,KAAK,EAAE;gBAAEwC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAA9C,QAAA,gBAC1D5E,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBAAIiF,KAAK,EAAE;oBAAEE,UAAU,EAAE;kBAAU,CAAE;kBAAAP,QAAA,gBACnC5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEqB,SAAS,EAAE,MAAM;sBAAER,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhF,OAAA;gBAAA4E,QAAA,EACGlE,IAAI,CAACM,IAAI,CAACsF,GAAG,CAACsC,GAAG,iBAChB5I,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEgE,GAAG,CAACC;kBAAM;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3EhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,EAAEgE,GAAG,CAACE;kBAAW;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChFhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvD5E,OAAA;sBAAMiF,KAAK,EAAE;wBACXE,UAAU,EACRyD,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,GACzCJ,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,GACvCJ,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,SAAS;wBACvDtD,KAAK,EACHkD,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,GACzCJ,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,GACvCJ,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,SAAS;wBACvD5D,OAAO,EAAE,SAAS;wBAClBG,YAAY,EAAE,KAAK;wBACnBO,QAAQ,EAAE;sBACZ,CAAE;sBAAAlB,QAAA,EACCgE,GAAG,CAACG,MAAM,CAAC5E,OAAO,CAAC,IAAI,EAAE,GAAG;oBAAC;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE,gBAAgB;sBAAEZ,QAAQ,EAAE;oBAAQ,CAAE;oBAAAT,QAAA,eAC1E5E,OAAA;sBAAKiF,KAAK,EAAE;wBACVO,QAAQ,EAAE,QAAQ;wBAClByD,YAAY,EAAE,UAAU;wBACxBC,UAAU,EAAE,QAAQ;wBACpBpD,QAAQ,EAAE,MAAM;wBAChBJ,KAAK,EAAE;sBACT,CAAE;sBAAAd,QAAA,EACCgE,GAAG,CAACO,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACD,IAAI,CAACE,KAAK,CAACV,GAAG,CAACO,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;oBAAK;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhF,OAAA;oBAAIiF,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEa,MAAM,EAAE,gBAAgB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAlB,QAAA,EACxE,IAAIwC,IAAI,CAACwB,GAAG,CAACY,UAAU,CAAC,CAACzC,cAAc,CAAC;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA,GAjCE4D,GAAG,CAACC,MAAM;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkCf,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAELtE,IAAI,CAACM,IAAI,CAACyI,MAAM,KAAK,CAAC,iBACrBzJ,OAAA;YAAKiF,KAAK,EAAE;cACVwB,SAAS,EAAE,QAAQ;cACnBrB,OAAO,EAAE,MAAM;cACfM,KAAK,EAAE,MAAM;cACbP,UAAU,EAAE,SAAS;cACrBI,YAAY,EAAE,MAAM;cACpBmB,SAAS,EAAE;YACb,CAAE;YAAA9B,QAAA,gBACA5E,OAAA;cAAKiF,KAAK,EAAE;gBAAEa,QAAQ,EAAE,MAAM;gBAAEe,YAAY,EAAE;cAAO,CAAE;cAAAjC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEhF,OAAA;cAAA4E,QAAA,EAAK;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9E,EAAA,CAx1BQD,cAAc;EAAA,QACIL,OAAO,EACfC,WAAW;AAAA;AAAA6J,EAAA,GAFrBzJ,cAAc;AA01BvB,eAAeA,cAAc;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}