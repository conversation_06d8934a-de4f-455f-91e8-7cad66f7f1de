{"ast": null, "code": "'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport adapters from \"../adapters/adapters.js\";\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError(null, config);\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n  config.headers = AxiosHeaders.from(config.headers);\n\n  // Transform request data\n  config.data = transformData.call(config, config.transformRequest);\n  if (['post', 'put', 'patch'].indexOf(config.method) !== -1) {\n    config.headers.setContentType('application/x-www-form-urlencoded', false);\n  }\n  const adapter = adapters.getAdapter(config.adapter || defaults.adapter);\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(config, config.transformResponse, response);\n    response.headers = AxiosHeaders.from(response.headers);\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(config, config.transformResponse, reason.response);\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n    return Promise.reject(reason);\n  });\n}", "map": {"version": 3, "names": ["transformData", "isCancel", "defaults", "CanceledError", "AxiosHeaders", "adapters", "throwIfCancellationRequested", "config", "cancelToken", "throwIfRequested", "signal", "aborted", "dispatchRequest", "headers", "from", "data", "call", "transformRequest", "indexOf", "method", "setContentType", "adapter", "getAdapter", "then", "onAdapterResolution", "response", "transformResponse", "onAdapterRejection", "reason", "Promise", "reject"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/node_modules/axios/lib/core/dispatchRequest.js"], "sourcesContent": ["'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport adapters from \"../adapters/adapters.js\";\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError(null, config);\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  config.headers = AxiosHeaders.from(config.headers);\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.transformRequest\n  );\n\n  if (['post', 'put', 'patch'].indexOf(config.method) !== -1) {\n    config.headers.setContentType('application/x-www-form-urlencoded', false);\n  }\n\n  const adapter = adapters.getAdapter(config.adapter || defaults.adapter);\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      config.transformResponse,\n      response\n    );\n\n    response.headers = AxiosHeaders.from(response.headers);\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          config.transformResponse,\n          reason.response\n        );\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,QAAQ,MAAM,yBAAyB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACC,MAAM,EAAE;EAC5C,IAAIA,MAAM,CAACC,WAAW,EAAE;IACtBD,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAAC,CAAC;EACvC;EAEA,IAAIF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACG,MAAM,CAACC,OAAO,EAAE;IAC1C,MAAM,IAAIR,aAAa,CAAC,IAAI,EAAEI,MAAM,CAAC;EACvC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASK,eAAeA,CAACL,MAAM,EAAE;EAC9CD,4BAA4B,CAACC,MAAM,CAAC;EAEpCA,MAAM,CAACM,OAAO,GAAGT,YAAY,CAACU,IAAI,CAACP,MAAM,CAACM,OAAO,CAAC;;EAElD;EACAN,MAAM,CAACQ,IAAI,GAAGf,aAAa,CAACgB,IAAI,CAC9BT,MAAM,EACNA,MAAM,CAACU,gBACT,CAAC;EAED,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAACC,OAAO,CAACX,MAAM,CAACY,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IAC1DZ,MAAM,CAACM,OAAO,CAACO,cAAc,CAAC,mCAAmC,EAAE,KAAK,CAAC;EAC3E;EAEA,MAAMC,OAAO,GAAGhB,QAAQ,CAACiB,UAAU,CAACf,MAAM,CAACc,OAAO,IAAInB,QAAQ,CAACmB,OAAO,CAAC;EAEvE,OAAOA,OAAO,CAACd,MAAM,CAAC,CAACgB,IAAI,CAAC,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;IACjEnB,4BAA4B,CAACC,MAAM,CAAC;;IAEpC;IACAkB,QAAQ,CAACV,IAAI,GAAGf,aAAa,CAACgB,IAAI,CAChCT,MAAM,EACNA,MAAM,CAACmB,iBAAiB,EACxBD,QACF,CAAC;IAEDA,QAAQ,CAACZ,OAAO,GAAGT,YAAY,CAACU,IAAI,CAACW,QAAQ,CAACZ,OAAO,CAAC;IAEtD,OAAOY,QAAQ;EACjB,CAAC,EAAE,SAASE,kBAAkBA,CAACC,MAAM,EAAE;IACrC,IAAI,CAAC3B,QAAQ,CAAC2B,MAAM,CAAC,EAAE;MACrBtB,4BAA4B,CAACC,MAAM,CAAC;;MAEpC;MACA,IAAIqB,MAAM,IAAIA,MAAM,CAACH,QAAQ,EAAE;QAC7BG,MAAM,CAACH,QAAQ,CAACV,IAAI,GAAGf,aAAa,CAACgB,IAAI,CACvCT,MAAM,EACNA,MAAM,CAACmB,iBAAiB,EACxBE,MAAM,CAACH,QACT,CAAC;QACDG,MAAM,CAACH,QAAQ,CAACZ,OAAO,GAAGT,YAAY,CAACU,IAAI,CAACc,MAAM,CAACH,QAAQ,CAACZ,OAAO,CAAC;MACtE;IACF;IAEA,OAAOgB,OAAO,CAACC,MAAM,CAACF,MAAM,CAAC;EAC/B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}