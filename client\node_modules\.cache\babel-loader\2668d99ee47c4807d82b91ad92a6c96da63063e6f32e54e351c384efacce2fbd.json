{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\components\\\\TripCostSummary.js\";\nimport React from 'react';\nimport { formatCurrency } from '../utils/vehicleUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TripCostSummary({\n  tripCalculation,\n  selectedVehicle,\n  costBreakdown,\n  onProceedToBooking\n}) {\n  if (!tripCalculation || !selectedVehicle || !costBreakdown) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginTop: '30px',\n      padding: '25px',\n      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n      borderRadius: '15px',\n      border: '2px solid #dee2e6'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        color: '#333',\n        marginBottom: '25px',\n        textAlign: 'center',\n        fontSize: '24px'\n      },\n      children: \"\\uD83D\\uDCB0 Trip Cost Summary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '15px',\n        marginBottom: '25px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666',\n            marginBottom: '5px'\n          },\n          children: \"Distance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            fontWeight: 'bold',\n            color: '#333'\n          },\n          children: [costBreakdown.roundedDistance, \" km\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '11px',\n            color: '#999'\n          },\n          children: [\"(Original: \", costBreakdown.mapDistance, \" km)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666',\n            marginBottom: '5px'\n          },\n          children: \"Vehicle Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            fontWeight: 'bold',\n            color: '#333'\n          },\n          children: selectedVehicle.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '11px',\n            color: '#999'\n          },\n          children: [formatCurrency(selectedVehicle.systemRate), \"/km\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666',\n            marginBottom: '5px'\n          },\n          children: \"Trip Cost\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            fontWeight: 'bold',\n            color: '#28a745'\n          },\n          children: formatCurrency(costBreakdown.baseCost)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), costBreakdown.accommodationCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666',\n            marginBottom: '5px'\n          },\n          children: \"Accommodation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            fontWeight: 'bold',\n            color: '#fd7e14'\n          },\n          children: formatCurrency(costBreakdown.accommodationCost)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        border: '1px solid #dee2e6',\n        marginBottom: '25px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#333',\n          marginBottom: '15px'\n        },\n        children: \"\\uD83D\\uDCCA Cost Breakdown\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Distance Calculation:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginLeft: '15px'\n          },\n          children: costBreakdown.breakdown.distance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Rate:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginLeft: '15px'\n          },\n          children: costBreakdown.breakdown.rate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Trip Cost Calculation:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginLeft: '15px'\n          },\n          children: costBreakdown.breakdown.calculation\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Driver Accommodation:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginLeft: '15px'\n          },\n          children: costBreakdown.breakdown.accommodation\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        style: {\n          margin: '15px 0',\n          border: 'none',\n          borderTop: '1px solid #dee2e6'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          fontSize: '18px',\n          fontWeight: 'bold'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#333'\n          },\n          children: \"Total Cost:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#28a745',\n            fontSize: '24px',\n            background: '#d4edda',\n            padding: '8px 16px',\n            borderRadius: '8px'\n          },\n          children: formatCurrency(costBreakdown.totalCost)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fff3cd',\n        border: '1px solid #ffeaa7',\n        borderRadius: '8px',\n        padding: '15px',\n        marginBottom: '25px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        style: {\n          color: '#856404',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83D\\uDCDD Important Notes:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          color: '#856404',\n          fontSize: '14px',\n          margin: 0,\n          paddingLeft: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Price includes all taxes and fees\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"10km additional distance added for practical purposes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Distance rounded up to nearest 10km for calculation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), costBreakdown.accommodationCost > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Accommodation cost for driver included\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"50% advance payment required to confirm booking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onProceedToBooking,\n        style: {\n          background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',\n          color: 'white',\n          border: 'none',\n          padding: '15px 40px',\n          borderRadius: '25px',\n          fontSize: '18px',\n          fontWeight: 'bold',\n          cursor: 'pointer',\n          boxShadow: '0 4px 15px rgba(40, 167, 69, 0.3)',\n          transition: 'all 0.3s ease',\n          minWidth: '200px'\n        },\n        onMouseOver: e => {\n          e.target.style.transform = 'translateY(-2px)';\n          e.target.style.boxShadow = '0 6px 20px rgba(40, 167, 69, 0.4)';\n        },\n        onMouseOut: e => {\n          e.target.style.transform = 'translateY(0)';\n          e.target.style.boxShadow = '0 4px 15px rgba(40, 167, 69, 0.3)';\n        },\n        children: \"\\uD83D\\uDE80 Proceed to Booking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginTop: '15px',\n        fontSize: '14px',\n        color: '#666'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Advance Payment Required:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), \" \", formatCurrency(costBreakdown.totalCost * 0.5), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: '12px'\n        },\n        children: \"(50% of total cost to confirm your booking)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = TripCostSummary;\nexport default TripCostSummary;\nvar _c;\n$RefreshReg$(_c, \"TripCostSummary\");", "map": {"version": 3, "names": ["React", "formatCurrency", "jsxDEV", "_jsxDEV", "TripCostSummary", "tripCalculation", "selectedVehicle", "costBreakdown", "onProceedToBooking", "style", "marginTop", "padding", "background", "borderRadius", "border", "children", "color", "marginBottom", "textAlign", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "fontWeight", "roundedDistance", "mapDistance", "name", "systemRate", "baseCost", "accommodationCost", "marginLeft", "breakdown", "distance", "rate", "calculation", "accommodation", "margin", "borderTop", "justifyContent", "alignItems", "totalCost", "paddingLeft", "onClick", "cursor", "boxShadow", "transition", "min<PERSON><PERSON><PERSON>", "onMouseOver", "e", "target", "transform", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/components/TripCostSummary.js"], "sourcesContent": ["import React from 'react';\nimport { formatCurrency } from '../utils/vehicleUtils';\n\nfunction TripCostSummary({ \n  tripCalculation, \n  selectedVehicle, \n  costBreakdown, \n  onProceedToBooking \n}) {\n  \n  if (!tripCalculation || !selectedVehicle || !costBreakdown) {\n    return null;\n  }\n\n  return (\n    <div style={{\n      marginTop: '30px',\n      padding: '25px',\n      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n      borderRadius: '15px',\n      border: '2px solid #dee2e6'\n    }}>\n      <h2 style={{ \n        color: '#333', \n        marginBottom: '25px',\n        textAlign: 'center',\n        fontSize: '24px'\n      }}>\n        💰 Trip Cost Summary\n      </h2>\n\n      {/* Trip Details */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '15px',\n        marginBottom: '25px'\n      }}>\n        <div style={{\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        }}>\n          <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Distance</div>\n          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#333' }}>\n            {costBreakdown.roundedDistance} km\n          </div>\n          <div style={{ fontSize: '11px', color: '#999' }}>\n            (Original: {costBreakdown.mapDistance} km)\n          </div>\n        </div>\n\n        <div style={{\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        }}>\n          <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Vehicle Type</div>\n          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#333' }}>\n            {selectedVehicle.name}\n          </div>\n          <div style={{ fontSize: '11px', color: '#999' }}>\n            {formatCurrency(selectedVehicle.systemRate)}/km\n          </div>\n        </div>\n\n        <div style={{\n          background: 'white',\n          padding: '15px',\n          borderRadius: '10px',\n          border: '1px solid #dee2e6'\n        }}>\n          <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Trip Cost</div>\n          <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#28a745' }}>\n            {formatCurrency(costBreakdown.baseCost)}\n          </div>\n        </div>\n\n        {costBreakdown.accommodationCost > 0 && (\n          <div style={{\n            background: 'white',\n            padding: '15px',\n            borderRadius: '10px',\n            border: '1px solid #dee2e6'\n          }}>\n            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Accommodation</div>\n            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fd7e14' }}>\n              {formatCurrency(costBreakdown.accommodationCost)}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Cost Breakdown Details */}\n      <div style={{\n        background: 'white',\n        padding: '20px',\n        borderRadius: '10px',\n        border: '1px solid #dee2e6',\n        marginBottom: '25px'\n      }}>\n        <h4 style={{ color: '#333', marginBottom: '15px' }}>📊 Cost Breakdown</h4>\n        \n        <div style={{ marginBottom: '10px' }}>\n          <strong>Distance Calculation:</strong>\n          <div style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>\n            {costBreakdown.breakdown.distance}\n          </div>\n        </div>\n\n        <div style={{ marginBottom: '10px' }}>\n          <strong>Rate:</strong>\n          <div style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>\n            {costBreakdown.breakdown.rate}\n          </div>\n        </div>\n\n        <div style={{ marginBottom: '10px' }}>\n          <strong>Trip Cost Calculation:</strong>\n          <div style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>\n            {costBreakdown.breakdown.calculation}\n          </div>\n        </div>\n\n        <div style={{ marginBottom: '10px' }}>\n          <strong>Driver Accommodation:</strong>\n          <div style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>\n            {costBreakdown.breakdown.accommodation}\n          </div>\n        </div>\n\n        <hr style={{ margin: '15px 0', border: 'none', borderTop: '1px solid #dee2e6' }} />\n\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center',\n          fontSize: '18px',\n          fontWeight: 'bold'\n        }}>\n          <span style={{ color: '#333' }}>Total Cost:</span>\n          <span style={{ \n            color: '#28a745',\n            fontSize: '24px',\n            background: '#d4edda',\n            padding: '8px 16px',\n            borderRadius: '8px'\n          }}>\n            {formatCurrency(costBreakdown.totalCost)}\n          </span>\n        </div>\n      </div>\n\n      {/* Additional Information */}\n      <div style={{\n        background: '#fff3cd',\n        border: '1px solid #ffeaa7',\n        borderRadius: '8px',\n        padding: '15px',\n        marginBottom: '25px'\n      }}>\n        <h5 style={{ color: '#856404', marginBottom: '10px' }}>📝 Important Notes:</h5>\n        <ul style={{ color: '#856404', fontSize: '14px', margin: 0, paddingLeft: '20px' }}>\n          <li>Price includes all taxes and fees</li>\n          <li>10km additional distance added for practical purposes</li>\n          <li>Distance rounded up to nearest 10km for calculation</li>\n          {costBreakdown.accommodationCost > 0 && (\n            <li>Accommodation cost for driver included</li>\n          )}\n          <li>50% advance payment required to confirm booking</li>\n        </ul>\n      </div>\n\n      {/* Proceed to Booking Button */}\n      <div style={{ textAlign: 'center' }}>\n        <button\n          onClick={onProceedToBooking}\n          style={{\n            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '15px 40px',\n            borderRadius: '25px',\n            fontSize: '18px',\n            fontWeight: 'bold',\n            cursor: 'pointer',\n            boxShadow: '0 4px 15px rgba(40, 167, 69, 0.3)',\n            transition: 'all 0.3s ease',\n            minWidth: '200px'\n          }}\n          onMouseOver={(e) => {\n            e.target.style.transform = 'translateY(-2px)';\n            e.target.style.boxShadow = '0 6px 20px rgba(40, 167, 69, 0.4)';\n          }}\n          onMouseOut={(e) => {\n            e.target.style.transform = 'translateY(0)';\n            e.target.style.boxShadow = '0 4px 15px rgba(40, 167, 69, 0.3)';\n          }}\n        >\n          🚀 Proceed to Booking\n        </button>\n      </div>\n\n      {/* Advance Payment Info */}\n      <div style={{\n        textAlign: 'center',\n        marginTop: '15px',\n        fontSize: '14px',\n        color: '#666'\n      }}>\n        <strong>Advance Payment Required:</strong> {formatCurrency(costBreakdown.totalCost * 0.5)}\n        <br />\n        <span style={{ fontSize: '12px' }}>\n          (50% of total cost to confirm your booking)\n        </span>\n      </div>\n    </div>\n  );\n}\n\nexport default TripCostSummary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,eAAeA,CAAC;EACvBC,eAAe;EACfC,eAAe;EACfC,aAAa;EACbC;AACF,CAAC,EAAE;EAED,IAAI,CAACH,eAAe,IAAI,CAACC,eAAe,IAAI,CAACC,aAAa,EAAE;IAC1D,OAAO,IAAI;EACb;EAEA,oBACEJ,OAAA;IAAKM,KAAK,EAAE;MACVC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBACAZ,OAAA;MAAIM,KAAK,EAAE;QACTO,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE;MACZ,CAAE;MAAAJ,QAAA,EAAC;IAEH;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLpB,OAAA;MAAKM,KAAK,EAAE;QACVe,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXT,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAZ,OAAA;QAAKM,KAAK,EAAE;UACVG,UAAU,EAAE,OAAO;UACnBD,OAAO,EAAE,MAAM;UACfE,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,gBACAZ,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EAAC;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpFpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEQ,UAAU,EAAE,MAAM;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,GACjER,aAAa,CAACqB,eAAe,EAAC,KACjC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,GAAC,aACpC,EAACR,aAAa,CAACsB,WAAW,EAAC,MACxC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKM,KAAK,EAAE;UACVG,UAAU,EAAE,OAAO;UACnBD,OAAO,EAAE,MAAM;UACfE,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,gBACAZ,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxFpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEQ,UAAU,EAAE,MAAM;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EACjET,eAAe,CAACwB;QAAI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,GAC7Cd,cAAc,CAACK,eAAe,CAACyB,UAAU,CAAC,EAAC,KAC9C;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKM,KAAK,EAAE;UACVG,UAAU,EAAE,OAAO;UACnBD,OAAO,EAAE,MAAM;UACfE,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,gBACAZ,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrFpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEQ,UAAU,EAAE,MAAM;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAD,QAAA,EACpEd,cAAc,CAACM,aAAa,CAACyB,QAAQ;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELhB,aAAa,CAAC0B,iBAAiB,GAAG,CAAC,iBAClC9B,OAAA;QAAKM,KAAK,EAAE;UACVG,UAAU,EAAE,OAAO;UACnBD,OAAO,EAAE,MAAM;UACfE,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAC,QAAA,gBACAZ,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzFpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEQ,UAAU,EAAE,MAAM;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAD,QAAA,EACpEd,cAAc,CAACM,aAAa,CAAC0B,iBAAiB;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpB,OAAA;MAAKM,KAAK,EAAE;QACVG,UAAU,EAAE,OAAO;QACnBD,OAAO,EAAE,MAAM;QACfE,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,mBAAmB;QAC3BG,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAZ,OAAA;QAAIM,KAAK,EAAE;UAAEO,KAAK,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE1EpB,OAAA;QAAKM,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnCZ,OAAA;UAAAY,QAAA,EAAQ;QAAqB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEkB,UAAU,EAAE;UAAO,CAAE;UAAAnB,QAAA,EACjER,aAAa,CAAC4B,SAAS,CAACC;QAAQ;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKM,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnCZ,OAAA;UAAAY,QAAA,EAAQ;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtBpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEkB,UAAU,EAAE;UAAO,CAAE;UAAAnB,QAAA,EACjER,aAAa,CAAC4B,SAAS,CAACE;QAAI;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKM,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnCZ,OAAA;UAAAY,QAAA,EAAQ;QAAsB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvCpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEkB,UAAU,EAAE;UAAO,CAAE;UAAAnB,QAAA,EACjER,aAAa,CAAC4B,SAAS,CAACG;QAAW;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAKM,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnCZ,OAAA;UAAAY,QAAA,EAAQ;QAAqB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCpB,OAAA;UAAKM,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEH,KAAK,EAAE,MAAM;YAAEkB,UAAU,EAAE;UAAO,CAAE;UAAAnB,QAAA,EACjER,aAAa,CAAC4B,SAAS,CAACI;QAAa;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QAAIM,KAAK,EAAE;UAAE+B,MAAM,EAAE,QAAQ;UAAE1B,MAAM,EAAE,MAAM;UAAE2B,SAAS,EAAE;QAAoB;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnFpB,OAAA;QAAKM,KAAK,EAAE;UACVe,OAAO,EAAE,MAAM;UACfkB,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBxB,QAAQ,EAAE,MAAM;UAChBQ,UAAU,EAAE;QACd,CAAE;QAAAZ,QAAA,gBACAZ,OAAA;UAAMM,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDpB,OAAA;UAAMM,KAAK,EAAE;YACXO,KAAK,EAAE,SAAS;YAChBG,QAAQ,EAAE,MAAM;YAChBP,UAAU,EAAE,SAAS;YACrBD,OAAO,EAAE,UAAU;YACnBE,YAAY,EAAE;UAChB,CAAE;UAAAE,QAAA,EACCd,cAAc,CAACM,aAAa,CAACqC,SAAS;QAAC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKM,KAAK,EAAE;QACVG,UAAU,EAAE,SAAS;QACrBE,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE,MAAM;QACfM,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAZ,OAAA;QAAIM,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/EpB,OAAA;QAAIM,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEG,QAAQ,EAAE,MAAM;UAAEqB,MAAM,EAAE,CAAC;UAAEK,WAAW,EAAE;QAAO,CAAE;QAAA9B,QAAA,gBAChFZ,OAAA;UAAAY,QAAA,EAAI;QAAiC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CpB,OAAA;UAAAY,QAAA,EAAI;QAAqD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DpB,OAAA;UAAAY,QAAA,EAAI;QAAmD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3DhB,aAAa,CAAC0B,iBAAiB,GAAG,CAAC,iBAClC9B,OAAA;UAAAY,QAAA,EAAI;QAAsC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC/C,eACDpB,OAAA;UAAAY,QAAA,EAAI;QAA+C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNpB,OAAA;MAAKM,KAAK,EAAE;QAAES,SAAS,EAAE;MAAS,CAAE;MAAAH,QAAA,eAClCZ,OAAA;QACE2C,OAAO,EAAEtC,kBAAmB;QAC5BC,KAAK,EAAE;UACLG,UAAU,EAAE,mDAAmD;UAC/DI,KAAK,EAAE,OAAO;UACdF,MAAM,EAAE,MAAM;UACdH,OAAO,EAAE,WAAW;UACpBE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,MAAM;UAChBQ,UAAU,EAAE,MAAM;UAClBoB,MAAM,EAAE,SAAS;UACjBC,SAAS,EAAE,mCAAmC;UAC9CC,UAAU,EAAE,eAAe;UAC3BC,QAAQ,EAAE;QACZ,CAAE;QACFC,WAAW,EAAGC,CAAC,IAAK;UAClBA,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAC6C,SAAS,GAAG,kBAAkB;UAC7CF,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAACuC,SAAS,GAAG,mCAAmC;QAChE,CAAE;QACFO,UAAU,EAAGH,CAAC,IAAK;UACjBA,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAC6C,SAAS,GAAG,eAAe;UAC1CF,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAACuC,SAAS,GAAG,mCAAmC;QAChE,CAAE;QAAAjC,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpB,OAAA;MAAKM,KAAK,EAAE;QACVS,SAAS,EAAE,QAAQ;QACnBR,SAAS,EAAE,MAAM;QACjBS,QAAQ,EAAE,MAAM;QAChBH,KAAK,EAAE;MACT,CAAE;MAAAD,QAAA,gBACAZ,OAAA;QAAAY,QAAA,EAAQ;MAAyB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACtB,cAAc,CAACM,aAAa,CAACqC,SAAS,GAAG,GAAG,CAAC,eACzFzC,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpB,OAAA;QAAMM,KAAK,EAAE;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACiC,EAAA,GAzNQpD,eAAe;AA2NxB,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}