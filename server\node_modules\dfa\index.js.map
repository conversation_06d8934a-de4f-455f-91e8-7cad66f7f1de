{"version": 3, "file": "index.js", "sources": ["src/StateMachine.js"], "sourcesContent": ["const INITIAL_STATE = 1;\nconst FAIL_STATE = 0;\n\n/**\n * A StateMachine represents a deterministic finite automaton.\n * It can perform matches over a sequence of values, similar to a regular expression.\n */\nexport default class StateMachine {\n  constructor(dfa) {\n    this.stateTable = dfa.stateTable;\n    this.accepting = dfa.accepting;\n    this.tags = dfa.tags;\n  }\n\n  /**\n   * Returns an iterable object that yields pattern matches over the input sequence.\n   * Matches are of the form [startIndex, endIndex, tags].\n   */\n  match(str) {\n    let self = this;\n    return {\n      *[Symbol.iterator]() {\n        let state = INITIAL_STATE;\n        let startRun = null;\n        let lastAccepting = null;\n        let lastState = null;\n\n        for (let p = 0; p < str.length; p++) {\n          let c = str[p];\n\n          lastState = state;\n          state = self.stateTable[state][c];\n\n          if (state === FAIL_STATE) {\n            // yield the last match if any\n            if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n              yield [startRun, lastAccepting, self.tags[lastState]];\n            }\n\n            // reset the state as if we started over from the initial state\n            state = self.stateTable[INITIAL_STATE][c];\n            startRun = null;\n          }\n\n          // start a run if not in the failure state\n          if (state !== FAIL_STATE && startRun == null) {\n            startRun = p;\n          }\n\n          // if accepting, mark the potential match end\n          if (self.accepting[state]) {\n            lastAccepting = p;\n          }\n\n          // reset the state to the initial state if we get into the failure state\n          if (state === FAIL_STATE) {\n            state = INITIAL_STATE;\n          }\n        }\n\n        // yield the last match if any\n        if (startRun != null && lastAccepting != null && lastAccepting >= startRun) {\n          yield [startRun, lastAccepting, self.tags[state]];\n        }\n      }\n    };\n  }\n\n  /**\n   * For each match over the input sequence, action functions matching\n   * the tag definitions in the input pattern are called with the startIndex,\n   * endIndex, and sub-match sequence.\n   */\n  apply(str, actions) {\n    for (let [start, end, tags] of this.match(str)) {\n      for (let tag of tags) {\n        if (typeof actions[tag] === 'function') {\n          actions[tag](start, end, str.slice(start, end + 1));\n        }\n      }\n    }\n  }\n}\n"], "names": ["INITIAL_STATE", "FAIL_STATE", "StateMachine", "constructor", "dfa", "stateTable", "accepting", "tags", "match", "str", "self", "Symbol", "iterator", "state", "startRun", "lastAccepting", "lastState", "p", "length", "c", "apply", "actions", "start", "end", "tag", "slice"], "mappings": ";;AAAA,IAAMA,aAAa,GAAG,CAAtB;AACA,IAAMC,UAAU,GAAG,CAAnB;;;;;;AAMA,AAAe,MAAMC,YAAN,CAAmB;EAChCC,WAAW,CAACC,GAAD,EAAM;SACVC,UAAL,GAAkBD,GAAG,CAACC,UAAtB;SACKC,SAAL,GAAiBF,GAAG,CAACE,SAArB;SACKC,IAAL,GAAYH,GAAG,CAACG,IAAhB;;;;;;;;EAOFC,KAAK,CAACC,GAAD,EAAM;QACLC,IAAI,GAAG,IAAX;WACO;QACHC,MAAM,CAACC,QAAT,IAAqB;YACfC,KAAK,GAAGb,aAAZ;YACIc,QAAQ,GAAG,IAAf;YACIC,aAAa,GAAG,IAApB;YACIC,SAAS,GAAG,IAAhB;;aAEK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,GAAG,CAACS,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;cAC/BE,CAAC,GAAGV,GAAG,CAACQ,CAAD,CAAX;UAEAD,SAAS,GAAGH,KAAZ;UACAA,KAAK,GAAGH,IAAI,CAACL,UAAL,CAAgBQ,KAAhB,EAAuBM,CAAvB,CAAR;;cAEIN,KAAK,KAAKZ,UAAd,EAA0B;;gBAEpBa,QAAQ,IAAI,IAAZ,IAAoBC,aAAa,IAAI,IAArC,IAA6CA,aAAa,IAAID,QAAlE,EAA4E;oBACpE,CAACA,QAAD,EAAWC,aAAX,EAA0BL,IAAI,CAACH,IAAL,CAAUS,SAAV,CAA1B,CAAN;aAHsB;;;YAOxBH,KAAK,GAAGH,IAAI,CAACL,UAAL,CAAgBL,aAAhB,EAA+BmB,CAA/B,CAAR;YACAL,QAAQ,GAAG,IAAX;WAdiC;;;cAkB/BD,KAAK,KAAKZ,UAAV,IAAwBa,QAAQ,IAAI,IAAxC,EAA8C;YAC5CA,QAAQ,GAAGG,CAAX;WAnBiC;;;cAuB/BP,IAAI,CAACJ,SAAL,CAAeO,KAAf,CAAJ,EAA2B;YACzBE,aAAa,GAAGE,CAAhB;WAxBiC;;;cA4B/BJ,KAAK,KAAKZ,UAAd,EAA0B;YACxBY,KAAK,GAAGb,aAAR;;SAnCe;;;YAwCfc,QAAQ,IAAI,IAAZ,IAAoBC,aAAa,IAAI,IAArC,IAA6CA,aAAa,IAAID,QAAlE,EAA4E;gBACpE,CAACA,QAAD,EAAWC,aAAX,EAA0BL,IAAI,CAACH,IAAL,CAAUM,KAAV,CAA1B,CAAN;;;;KA1CN;;;;;;;;;EAqDFO,KAAK,CAACX,GAAD,EAAMY,OAAN,EAAe;SACb,IAAI,CAACC,KAAD,EAAQC,GAAR,EAAahB,IAAb,CAAT,IAA+B,KAAKC,KAAL,CAAWC,GAAX,CAA/B,EAAgD;WACzC,IAAIe,GAAT,IAAgBjB,IAAhB,EAAsB;YAChB,OAAOc,OAAO,CAACG,GAAD,CAAd,KAAwB,UAA5B,EAAwC;UACtCH,OAAO,CAACG,GAAD,CAAP,CAAaF,KAAb,EAAoBC,GAApB,EAAyBd,GAAG,CAACgB,KAAJ,CAAUH,KAAV,EAAiBC,GAAG,GAAG,CAAvB,CAAzB;;;;;;;;;;"}