{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\pages\\\\DriverDashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './DriverDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DriverDashboard = () => {\n  _s();\n  var _user$profile;\n  const [activeTab, setActiveTab] = useState('vehicles');\n  const [vehicles, setVehicles] = useState([]);\n  const [availableBookings, setAvailableBookings] = useState([]);\n  const [myBookings, setMyBookings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  const token = localStorage.getItem('token');\n  useEffect(() => {\n    if (!token || user.role !== 'driver') {\n      navigate('/login');\n      return;\n    }\n    loadData();\n  }, [activeTab]);\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      if (activeTab === 'vehicles') {\n        await loadVehicles();\n      } else if (activeTab === 'bookings') {\n        await loadAvailableBookings();\n      } else if (activeTab === 'my-bookings') {\n        await loadMyBookings();\n      }\n    } catch (error) {\n      console.error('Error loading data:', error);\n    }\n    setLoading(false);\n  };\n  const loadVehicles = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/vehicles', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setVehicles(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading vehicles:', error);\n    }\n  };\n  const loadAvailableBookings = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/available-bookings', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setAvailableBookings(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading bookings:', error);\n    }\n  };\n  const loadMyBookings = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/my-bookings', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setMyBookings(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading my bookings:', error);\n    }\n  };\n  const acceptBooking = async bookingId => {\n    try {\n      const response = await fetch(`http://localhost:5001/api/driver/accept-booking/${bookingId}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Booking accepted successfully!');\n        loadAvailableBookings();\n        loadMyBookings();\n      } else {\n        alert('Failed to accept booking: ' + data.message);\n      }\n    } catch (error) {\n      console.error('Error accepting booking:', error);\n      alert('Error accepting booking');\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"driver-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Driver Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Welcome, \", ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.first_name) || 'Driver']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"logout-btn\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"dashboard-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'vehicles' ? 'active' : '',\n        onClick: () => setActiveTab('vehicles'),\n        children: \"My Vehicles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'bookings' ? 'active' : '',\n        onClick: () => setActiveTab('bookings'),\n        children: \"Available Bookings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'my-bookings' ? 'active' : '',\n        onClick: () => setActiveTab('my-bookings'),\n        children: \"My Bookings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-content\",\n      children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 21\n      }, this), activeTab === 'vehicles' && /*#__PURE__*/_jsxDEV(VehiclesSection, {\n        vehicles: vehicles,\n        onReload: loadVehicles,\n        token: token\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), activeTab === 'bookings' && /*#__PURE__*/_jsxDEV(AvailableBookingsSection, {\n        bookings: availableBookings,\n        onAccept: acceptBooking\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), activeTab === 'my-bookings' && /*#__PURE__*/_jsxDEV(MyBookingsSection, {\n        bookings: myBookings\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n\n// Vehicles Section Component\n_s(DriverDashboard, \"+KGp7Uxx2eaOWEDVUASMTxHvAg4=\", false, function () {\n  return [useNavigate];\n});\n_c = DriverDashboard;\nconst VehiclesSection = ({\n  vehicles,\n  onReload,\n  token\n}) => {\n  _s2();\n  const [showAddForm, setShowAddForm] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vehicles-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"My Vehicles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"add-btn\",\n        onClick: () => setShowAddForm(true),\n        children: \"Add Vehicle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddVehicleForm, {\n      onClose: () => setShowAddForm(false),\n      onSuccess: () => {\n        setShowAddForm(false);\n        onReload();\n      },\n      token: token\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"vehicles-grid\",\n      children: [vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"vehicle-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: vehicle.make_model\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Type:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 16\n          }, this), \" \", vehicle.category_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Registration:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 16\n          }, this), \" \", vehicle.registration_number]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Capacity:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 16\n          }, this), \" \", vehicle.seating_capacity, \" seats\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 16\n          }, this), \" \", vehicle.is_active ? 'Active' : 'Inactive']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, vehicle.vehicle_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)), vehicles.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"no-data\",\n        children: \"No vehicles added yet. Add your first vehicle!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n\n// Available Bookings Section Component\n_s2(VehiclesSection, \"H+ZOox/7mQfpWCTEQ7PJKozNc98=\");\n_c2 = VehiclesSection;\nconst AvailableBookingsSection = ({\n  bookings,\n  onAccept\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bookings-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Available Bookings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bookings-list\",\n      children: [bookings.map(booking => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"booking-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"booking-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Trip from \", booking.pickup_location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Destinations:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 18\n            }, this), \" \", JSON.parse(booking.destinations).join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 18\n            }, this), \" \", new Date(booking.start_date).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Time:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 18\n            }, this), \" \", booking.start_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Travelers:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 18\n            }, this), \" \", booking.travelers_count]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Distance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 18\n            }, this), \" \", booking.total_distance_km, \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Cost:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 18\n            }, this), \" Rs. \", booking.total_cost]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Vehicle Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 18\n            }, this), \" \", booking.category_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"accept-btn\",\n          onClick: () => onAccept(booking.booking_id),\n          children: \"Accept Booking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, booking.booking_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)), bookings.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"no-data\",\n        children: \"No available bookings matching your vehicle types.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n\n// My Bookings Section Component\n_c3 = AvailableBookingsSection;\nconst MyBookingsSection = ({\n  bookings\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bookings-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"My Bookings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bookings-list\",\n      children: [bookings.map(booking => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"booking-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"booking-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Trip from \", booking.pickup_location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Destinations:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 18\n            }, this), \" \", JSON.parse(booking.destinations).join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 18\n            }, this), \" \", new Date(booking.start_date).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Time:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 18\n            }, this), \" \", booking.start_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Travelers:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 18\n            }, this), \" \", booking.travelers_count]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Cost:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 18\n            }, this), \" Rs. \", booking.total_cost]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 18\n            }, this), \" \", booking.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Payment:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 18\n            }, this), \" \", booking.payment_status || 'Pending']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-badge ${booking.status}`,\n          children: booking.status.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, booking.booking_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)), bookings.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"no-data\",\n        children: \"No bookings yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n\n// Add Vehicle Form Component\n_c4 = MyBookingsSection;\nconst AddVehicleForm = ({\n  onClose,\n  onSuccess,\n  token\n}) => {\n  _s3();\n  const [formData, setFormData] = useState({\n    category_id: '',\n    make_model: '',\n    registration_number: '',\n    year_manufactured: '',\n    color: '',\n    seating_capacity: '',\n    insurance_expiry: ''\n  });\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    loadCategories();\n  }, []);\n  const loadCategories = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/bookings/vehicle-categories');\n      const data = await response.json();\n      if (data.success) {\n        setCategories(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading categories:', error);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/vehicles', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Vehicle added successfully!');\n        onSuccess();\n      } else {\n        alert('Failed to add vehicle: ' + data.message);\n      }\n    } catch (error) {\n      console.error('Error adding vehicle:', error);\n      alert('Error adding vehicle');\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Vehicle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-btn\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"vehicle-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Vehicle Category:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.category_id,\n            onChange: e => setFormData({\n              ...formData,\n              category_id: e.target.value\n            }),\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), categories.map(cat => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: cat.category_id,\n              children: cat.category_name\n            }, cat.category_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Make & Model:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData.make_model,\n            onChange: e => setFormData({\n              ...formData,\n              make_model: e.target.value\n            }),\n            placeholder: \"e.g., Toyota Corolla\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Registration Number:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData.registration_number,\n            onChange: e => setFormData({\n              ...formData,\n              registration_number: e.target.value\n            }),\n            placeholder: \"e.g., CAR-1234\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: formData.year_manufactured,\n            onChange: e => setFormData({\n              ...formData,\n              year_manufactured: e.target.value\n            }),\n            min: \"1990\",\n            max: \"2025\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Color:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData.color,\n            onChange: e => setFormData({\n              ...formData,\n              color: e.target.value\n            }),\n            placeholder: \"e.g., White\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Seating Capacity:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: formData.seating_capacity,\n            onChange: e => setFormData({\n              ...formData,\n              seating_capacity: e.target.value\n            }),\n            min: \"1\",\n            max: \"50\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Insurance Expiry:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: formData.insurance_expiry,\n            onChange: e => setFormData({\n              ...formData,\n              insurance_expiry: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            children: loading ? 'Adding...' : 'Add Vehicle'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 5\n  }, this);\n};\n_s3(AddVehicleForm, \"xy1swFzDbTInYYhF3nIn1N5LDkU=\");\n_c5 = AddVehicleForm;\nexport default DriverDashboard;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"DriverDashboard\");\n$RefreshReg$(_c2, \"VehiclesSection\");\n$RefreshReg$(_c3, \"AvailableBookingsSection\");\n$RefreshReg$(_c4, \"MyBookingsSection\");\n$RefreshReg$(_c5, \"AddVehicleForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "DriverDashboard", "_s", "_user$profile", "activeTab", "setActiveTab", "vehicles", "setVehicles", "availableBookings", "setAvailableBookings", "myBookings", "setMyBookings", "loading", "setLoading", "navigate", "user", "JSON", "parse", "localStorage", "getItem", "token", "role", "loadData", "loadVehicles", "loadAvailableBookings", "loadMyBookings", "error", "console", "response", "fetch", "headers", "data", "json", "success", "acceptBooking", "bookingId", "method", "alert", "message", "logout", "removeItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "profile", "first_name", "onClick", "VehiclesSection", "onReload", "AvailableBookingsSection", "bookings", "onAccept", "MyBookingsSection", "_c", "_s2", "showAddForm", "setShowAddForm", "AddVehicleForm", "onClose", "onSuccess", "map", "vehicle", "make_model", "category_name", "registration_number", "seating_capacity", "is_active", "vehicle_id", "length", "_c2", "booking", "pickup_location", "destinations", "join", "Date", "start_date", "toLocaleDateString", "start_time", "travelers_count", "total_distance_km", "total_cost", "booking_id", "_c3", "status", "payment_status", "toUpperCase", "_c4", "_s3", "formData", "setFormData", "category_id", "year_manufactured", "color", "insurance_expiry", "categories", "setCategories", "loadCategories", "handleSubmit", "e", "preventDefault", "body", "stringify", "onSubmit", "value", "onChange", "target", "required", "cat", "type", "placeholder", "min", "max", "disabled", "_c5", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/pages/DriverDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './DriverDashboard.css';\n\nconst DriverDashboard = () => {\n  const [activeTab, setActiveTab] = useState('vehicles');\n  const [vehicles, setVehicles] = useState([]);\n  const [availableBookings, setAvailableBookings] = useState([]);\n  const [myBookings, setMyBookings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const user = JSON.parse(localStorage.getItem('user') || '{}');\n  const token = localStorage.getItem('token');\n\n  useEffect(() => {\n    if (!token || user.role !== 'driver') {\n      navigate('/login');\n      return;\n    }\n    loadData();\n  }, [activeTab]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      if (activeTab === 'vehicles') {\n        await loadVehicles();\n      } else if (activeTab === 'bookings') {\n        await loadAvailableBookings();\n      } else if (activeTab === 'my-bookings') {\n        await loadMyBookings();\n      }\n    } catch (error) {\n      console.error('Error loading data:', error);\n    }\n    setLoading(false);\n  };\n\n  const loadVehicles = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/vehicles', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setVehicles(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading vehicles:', error);\n    }\n  };\n\n  const loadAvailableBookings = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/available-bookings', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setAvailableBookings(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading bookings:', error);\n    }\n  };\n\n  const loadMyBookings = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/my-bookings', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setMyBookings(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading my bookings:', error);\n    }\n  };\n\n  const acceptBooking = async (bookingId) => {\n    try {\n      const response = await fetch(`http://localhost:5001/api/driver/accept-booking/${bookingId}`, {\n        method: 'POST',\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Booking accepted successfully!');\n        loadAvailableBookings();\n        loadMyBookings();\n      } else {\n        alert('Failed to accept booking: ' + data.message);\n      }\n    } catch (error) {\n      console.error('Error accepting booking:', error);\n      alert('Error accepting booking');\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"driver-dashboard\">\n      <header className=\"dashboard-header\">\n        <h1>Driver Dashboard</h1>\n        <div className=\"header-actions\">\n          <span>Welcome, {user.profile?.first_name || 'Driver'}</span>\n          <button onClick={logout} className=\"logout-btn\">Logout</button>\n        </div>\n      </header>\n\n      <nav className=\"dashboard-nav\">\n        <button \n          className={activeTab === 'vehicles' ? 'active' : ''}\n          onClick={() => setActiveTab('vehicles')}\n        >\n          My Vehicles\n        </button>\n        <button \n          className={activeTab === 'bookings' ? 'active' : ''}\n          onClick={() => setActiveTab('bookings')}\n        >\n          Available Bookings\n        </button>\n        <button \n          className={activeTab === 'my-bookings' ? 'active' : ''}\n          onClick={() => setActiveTab('my-bookings')}\n        >\n          My Bookings\n        </button>\n      </nav>\n\n      <main className=\"dashboard-content\">\n        {loading && <div className=\"loading\">Loading...</div>}\n\n        {activeTab === 'vehicles' && (\n          <VehiclesSection vehicles={vehicles} onReload={loadVehicles} token={token} />\n        )}\n\n        {activeTab === 'bookings' && (\n          <AvailableBookingsSection \n            bookings={availableBookings} \n            onAccept={acceptBooking} \n          />\n        )}\n\n        {activeTab === 'my-bookings' && (\n          <MyBookingsSection bookings={myBookings} />\n        )}\n      </main>\n    </div>\n  );\n};\n\n// Vehicles Section Component\nconst VehiclesSection = ({ vehicles, onReload, token }) => {\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  return (\n    <div className=\"vehicles-section\">\n      <div className=\"section-header\">\n        <h2>My Vehicles</h2>\n        <button \n          className=\"add-btn\"\n          onClick={() => setShowAddForm(true)}\n        >\n          Add Vehicle\n        </button>\n      </div>\n\n      {showAddForm && (\n        <AddVehicleForm \n          onClose={() => setShowAddForm(false)}\n          onSuccess={() => {\n            setShowAddForm(false);\n            onReload();\n          }}\n          token={token}\n        />\n      )}\n\n      <div className=\"vehicles-grid\">\n        {vehicles.map(vehicle => (\n          <div key={vehicle.vehicle_id} className=\"vehicle-card\">\n            <h3>{vehicle.make_model}</h3>\n            <p><strong>Type:</strong> {vehicle.category_name}</p>\n            <p><strong>Registration:</strong> {vehicle.registration_number}</p>\n            <p><strong>Capacity:</strong> {vehicle.seating_capacity} seats</p>\n            <p><strong>Status:</strong> {vehicle.is_active ? 'Active' : 'Inactive'}</p>\n          </div>\n        ))}\n        {vehicles.length === 0 && (\n          <p className=\"no-data\">No vehicles added yet. Add your first vehicle!</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Available Bookings Section Component\nconst AvailableBookingsSection = ({ bookings, onAccept }) => {\n  return (\n    <div className=\"bookings-section\">\n      <h2>Available Bookings</h2>\n      <div className=\"bookings-list\">\n        {bookings.map(booking => (\n          <div key={booking.booking_id} className=\"booking-card\">\n            <div className=\"booking-info\">\n              <h3>Trip from {booking.pickup_location}</h3>\n              <p><strong>Destinations:</strong> {JSON.parse(booking.destinations).join(', ')}</p>\n              <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>\n              <p><strong>Time:</strong> {booking.start_time}</p>\n              <p><strong>Travelers:</strong> {booking.travelers_count}</p>\n              <p><strong>Distance:</strong> {booking.total_distance_km} km</p>\n              <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>\n              <p><strong>Vehicle Type:</strong> {booking.category_name}</p>\n            </div>\n            <button \n              className=\"accept-btn\"\n              onClick={() => onAccept(booking.booking_id)}\n            >\n              Accept Booking\n            </button>\n          </div>\n        ))}\n        {bookings.length === 0 && (\n          <p className=\"no-data\">No available bookings matching your vehicle types.</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// My Bookings Section Component\nconst MyBookingsSection = ({ bookings }) => {\n  return (\n    <div className=\"bookings-section\">\n      <h2>My Bookings</h2>\n      <div className=\"bookings-list\">\n        {bookings.map(booking => (\n          <div key={booking.booking_id} className=\"booking-card\">\n            <div className=\"booking-info\">\n              <h3>Trip from {booking.pickup_location}</h3>\n              <p><strong>Destinations:</strong> {JSON.parse(booking.destinations).join(', ')}</p>\n              <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>\n              <p><strong>Time:</strong> {booking.start_time}</p>\n              <p><strong>Travelers:</strong> {booking.travelers_count}</p>\n              <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>\n              <p><strong>Status:</strong> {booking.status}</p>\n              <p><strong>Payment:</strong> {booking.payment_status || 'Pending'}</p>\n            </div>\n            <div className={`status-badge ${booking.status}`}>\n              {booking.status.toUpperCase()}\n            </div>\n          </div>\n        ))}\n        {bookings.length === 0 && (\n          <p className=\"no-data\">No bookings yet.</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Add Vehicle Form Component\nconst AddVehicleForm = ({ onClose, onSuccess, token }) => {\n  const [formData, setFormData] = useState({\n    category_id: '',\n    make_model: '',\n    registration_number: '',\n    year_manufactured: '',\n    color: '',\n    seating_capacity: '',\n    insurance_expiry: ''\n  });\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      const response = await fetch('http://localhost:5001/api/bookings/vehicle-categories');\n      const data = await response.json();\n      if (data.success) {\n        setCategories(data.data);\n      }\n    } catch (error) {\n      console.error('Error loading categories:', error);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await fetch('http://localhost:5001/api/driver/vehicles', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        alert('Vehicle added successfully!');\n        onSuccess();\n      } else {\n        alert('Failed to add vehicle: ' + data.message);\n      }\n    } catch (error) {\n      console.error('Error adding vehicle:', error);\n      alert('Error adding vehicle');\n    }\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h3>Add New Vehicle</h3>\n          <button onClick={onClose} className=\"close-btn\">×</button>\n        </div>\n        <form onSubmit={handleSubmit} className=\"vehicle-form\">\n          <div className=\"form-group\">\n            <label>Vehicle Category:</label>\n            <select \n              value={formData.category_id}\n              onChange={(e) => setFormData({...formData, category_id: e.target.value})}\n              required\n            >\n              <option value=\"\">Select Category</option>\n              {categories.map(cat => (\n                <option key={cat.category_id} value={cat.category_id}>\n                  {cat.category_name}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div className=\"form-group\">\n            <label>Make & Model:</label>\n            <input \n              type=\"text\"\n              value={formData.make_model}\n              onChange={(e) => setFormData({...formData, make_model: e.target.value})}\n              placeholder=\"e.g., Toyota Corolla\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Registration Number:</label>\n            <input \n              type=\"text\"\n              value={formData.registration_number}\n              onChange={(e) => setFormData({...formData, registration_number: e.target.value})}\n              placeholder=\"e.g., CAR-1234\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Year:</label>\n            <input \n              type=\"number\"\n              value={formData.year_manufactured}\n              onChange={(e) => setFormData({...formData, year_manufactured: e.target.value})}\n              min=\"1990\"\n              max=\"2025\"\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Color:</label>\n            <input \n              type=\"text\"\n              value={formData.color}\n              onChange={(e) => setFormData({...formData, color: e.target.value})}\n              placeholder=\"e.g., White\"\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Seating Capacity:</label>\n            <input \n              type=\"number\"\n              value={formData.seating_capacity}\n              onChange={(e) => setFormData({...formData, seating_capacity: e.target.value})}\n              min=\"1\"\n              max=\"50\"\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label>Insurance Expiry:</label>\n            <input \n              type=\"date\"\n              value={formData.insurance_expiry}\n              onChange={(e) => setFormData({...formData, insurance_expiry: e.target.value})}\n              required\n            />\n          </div>\n          <div className=\"form-actions\">\n            <button type=\"button\" onClick={onClose}>Cancel</button>\n            <button type=\"submit\" disabled={loading}>\n              {loading ? 'Adding...' : 'Add Vehicle'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default DriverDashboard;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;EAC7D,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAE3CtB,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,KAAK,IAAIL,IAAI,CAACM,IAAI,KAAK,QAAQ,EAAE;MACpCP,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IACAQ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClB,SAAS,CAAC,CAAC;EAEf,MAAMkB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIT,SAAS,KAAK,UAAU,EAAE;QAC5B,MAAMmB,YAAY,CAAC,CAAC;MACtB,CAAC,MAAM,IAAInB,SAAS,KAAK,UAAU,EAAE;QACnC,MAAMoB,qBAAqB,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIpB,SAAS,KAAK,aAAa,EAAE;QACtC,MAAMqB,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;IACAb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACxEC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUV,KAAK;QAAG;MAChD,CAAC,CAAC;MACF,MAAMW,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB1B,WAAW,CAACwB,IAAI,CAACA,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMF,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,qDAAqD,EAAE;QAClFC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUV,KAAK;QAAG;MAChD,CAAC,CAAC;MACF,MAAMW,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBxB,oBAAoB,CAACsB,IAAI,CAACA,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,8CAA8C,EAAE;QAC3EC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUV,KAAK;QAAG;MAChD,CAAC,CAAC;MACF,MAAMW,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBtB,aAAa,CAACoB,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMQ,aAAa,GAAG,MAAOC,SAAS,IAAK;IACzC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMC,KAAK,CAAC,mDAAmDM,SAAS,EAAE,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACdN,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUV,KAAK;QAAG;MAChD,CAAC,CAAC;MACF,MAAMW,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBI,KAAK,CAAC,gCAAgC,CAAC;QACvCb,qBAAqB,CAAC,CAAC;QACvBC,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLY,KAAK,CAAC,4BAA4B,GAAGN,IAAI,CAACO,OAAO,CAAC;MACpD;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDW,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;EAED,MAAME,MAAM,GAAGA,CAAA,KAAM;IACnBrB,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;IAChCtB,YAAY,CAACsB,UAAU,CAAC,MAAM,CAAC;IAC/B1B,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEd,OAAA;IAAKyC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B1C,OAAA;MAAQyC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAClC1C,OAAA;QAAA0C,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB9C,OAAA;QAAKyC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1C,OAAA;UAAA0C,QAAA,GAAM,WAAS,EAAC,EAAAvC,aAAA,GAAAY,IAAI,CAACgC,OAAO,cAAA5C,aAAA,uBAAZA,aAAA,CAAc6C,UAAU,KAAI,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5D9C,OAAA;UAAQiD,OAAO,EAAEV,MAAO;UAACE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET9C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1C,OAAA;QACEyC,SAAS,EAAErC,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpD6C,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,UAAU,CAAE;QAAAqC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9C,OAAA;QACEyC,SAAS,EAAErC,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QACpD6C,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,UAAU,CAAE;QAAAqC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9C,OAAA;QACEyC,SAAS,EAAErC,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAG;QACvD6C,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,aAAa,CAAE;QAAAqC,QAAA,EAC5C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN9C,OAAA;MAAMyC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,GAChC9B,OAAO,iBAAIZ,OAAA;QAAKyC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEpD1C,SAAS,KAAK,UAAU,iBACvBJ,OAAA,CAACkD,eAAe;QAAC5C,QAAQ,EAAEA,QAAS;QAAC6C,QAAQ,EAAE5B,YAAa;QAACH,KAAK,EAAEA;MAAM;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC7E,EAEA1C,SAAS,KAAK,UAAU,iBACvBJ,OAAA,CAACoD,wBAAwB;QACvBC,QAAQ,EAAE7C,iBAAkB;QAC5B8C,QAAQ,EAAEpB;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CACF,EAEA1C,SAAS,KAAK,aAAa,iBAC1BJ,OAAA,CAACuD,iBAAiB;QAACF,QAAQ,EAAE3C;MAAW;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC3C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;;AAED;AAAA5C,EAAA,CA5JMD,eAAe;EAAA,QAMFH,WAAW;AAAA;AAAA0D,EAAA,GANxBvD,eAAe;AA6JrB,MAAMiD,eAAe,GAAGA,CAAC;EAAE5C,QAAQ;EAAE6C,QAAQ;EAAE/B;AAAM,CAAC,KAAK;EAAAqC,GAAA;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEI,OAAA;IAAKyC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B1C,OAAA;MAAKyC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1C,OAAA;QAAA0C,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpB9C,OAAA;QACEyC,SAAS,EAAC,SAAS;QACnBQ,OAAO,EAAEA,CAAA,KAAMU,cAAc,CAAC,IAAI,CAAE;QAAAjB,QAAA,EACrC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELY,WAAW,iBACV1D,OAAA,CAAC4D,cAAc;MACbC,OAAO,EAAEA,CAAA,KAAMF,cAAc,CAAC,KAAK,CAAE;MACrCG,SAAS,EAAEA,CAAA,KAAM;QACfH,cAAc,CAAC,KAAK,CAAC;QACrBR,QAAQ,CAAC,CAAC;MACZ,CAAE;MACF/B,KAAK,EAAEA;IAAM;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF,eAED9C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BpC,QAAQ,CAACyD,GAAG,CAACC,OAAO,iBACnBhE,OAAA;QAA8ByC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpD1C,OAAA;UAAA0C,QAAA,EAAKsB,OAAO,CAACC;QAAU;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B9C,OAAA;UAAA0C,QAAA,gBAAG1C,OAAA;YAAA0C,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACkB,OAAO,CAACE,aAAa;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrD9C,OAAA;UAAA0C,QAAA,gBAAG1C,OAAA;YAAA0C,QAAA,EAAQ;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACkB,OAAO,CAACG,mBAAmB;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnE9C,OAAA;UAAA0C,QAAA,gBAAG1C,OAAA;YAAA0C,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACkB,OAAO,CAACI,gBAAgB,EAAC,QAAM;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClE9C,OAAA;UAAA0C,QAAA,gBAAG1C,OAAA;YAAA0C,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACkB,OAAO,CAACK,SAAS,GAAG,QAAQ,GAAG,UAAU;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GALnEkB,OAAO,CAACM,UAAU;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMvB,CACN,CAAC,EACDxC,QAAQ,CAACiE,MAAM,KAAK,CAAC,iBACpBvE,OAAA;QAAGyC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACzE;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAW,GAAA,CA5CMP,eAAe;AAAAsB,GAAA,GAAftB,eAAe;AA6CrB,MAAME,wBAAwB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAC3D,oBACEtD,OAAA;IAAKyC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B1C,OAAA;MAAA0C,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3B9C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BW,QAAQ,CAACU,GAAG,CAACU,OAAO,iBACnBzE,OAAA;QAA8ByC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpD1C,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1C,OAAA;YAAA0C,QAAA,GAAI,YAAU,EAAC+B,OAAO,CAACC,eAAe;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9B,IAAI,CAACC,KAAK,CAACwD,OAAO,CAACE,YAAY,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAI+B,IAAI,CAACJ,OAAO,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjF9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACO,UAAU;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClD9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACQ,eAAe;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACS,iBAAiB,EAAC,KAAG;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChE9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAC2B,OAAO,CAACU,UAAU;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACP,aAAa;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN9C,OAAA;UACEyC,SAAS,EAAC,YAAY;UACtBQ,OAAO,EAAEA,CAAA,KAAMK,QAAQ,CAACmB,OAAO,CAACW,UAAU,CAAE;UAAA1C,QAAA,EAC7C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GAhBD2B,OAAO,CAACW,UAAU;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBvB,CACN,CAAC,EACDO,QAAQ,CAACkB,MAAM,KAAK,CAAC,iBACpBvE,OAAA;QAAGyC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAkD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC7E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAuC,GAAA,GAjCMjC,wBAAwB;AAkC9B,MAAMG,iBAAiB,GAAGA,CAAC;EAAEF;AAAS,CAAC,KAAK;EAC1C,oBACErD,OAAA;IAAKyC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B1C,OAAA;MAAA0C,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpB9C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BW,QAAQ,CAACU,GAAG,CAACU,OAAO,iBACnBzE,OAAA;QAA8ByC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpD1C,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1C,OAAA;YAAA0C,QAAA,GAAI,YAAU,EAAC+B,OAAO,CAACC,eAAe;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9B,IAAI,CAACC,KAAK,CAACwD,OAAO,CAACE,YAAY,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAI+B,IAAI,CAACJ,OAAO,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjF9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACO,UAAU;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClD9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACQ,eAAe;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAC2B,OAAO,CAACU,UAAU;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACa,MAAM;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD9C,OAAA;YAAA0C,QAAA,gBAAG1C,OAAA;cAAA0C,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACc,cAAc,IAAI,SAAS;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAE,gBAAgBgC,OAAO,CAACa,MAAM,EAAG;UAAA5C,QAAA,EAC9C+B,OAAO,CAACa,MAAM,CAACE,WAAW,CAAC;QAAC;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA,GAbE2B,OAAO,CAACW,UAAU;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcvB,CACN,CAAC,EACDO,QAAQ,CAACkB,MAAM,KAAK,CAAC,iBACpBvE,OAAA;QAAGyC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC3C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA2C,GAAA,GA9BMlC,iBAAiB;AA+BvB,MAAMK,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,SAAS;EAAE1C;AAAM,CAAC,KAAK;EAAAsE,GAAA;EACxD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC;IACvCiG,WAAW,EAAE,EAAE;IACf5B,UAAU,EAAE,EAAE;IACdE,mBAAmB,EAAE,EAAE;IACvB2B,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACT3B,gBAAgB,EAAE,EAAE;IACpB4B,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACdsG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMvE,QAAQ,GAAG,MAAMC,KAAK,CAAC,uDAAuD,CAAC;MACrF,MAAME,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBiE,aAAa,CAACnE,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAM0E,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBzF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACxEO,MAAM,EAAE,MAAM;QACdN,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUV,KAAK;QAClC,CAAC;QACDmF,IAAI,EAAEvF,IAAI,CAACwF,SAAS,CAACb,QAAQ;MAC/B,CAAC,CAAC;MAEF,MAAM5D,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBI,KAAK,CAAC,6BAA6B,CAAC;QACpCyB,SAAS,CAAC,CAAC;MACb,CAAC,MAAM;QACLzB,KAAK,CAAC,yBAAyB,GAAGN,IAAI,CAACO,OAAO,CAAC;MACjD;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CW,KAAK,CAAC,sBAAsB,CAAC;IAC/B;IACAxB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEb,OAAA;IAAKyC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1C,OAAA;QAAKyC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1C,OAAA;UAAA0C,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxB9C,OAAA;UAAQiD,OAAO,EAAEY,OAAQ;UAACpB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACN9C,OAAA;QAAMyG,QAAQ,EAAEL,YAAa;QAAC3D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACpD1C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChC9C,OAAA;YACE0G,KAAK,EAAEf,QAAQ,CAACE,WAAY;YAC5Bc,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEE,WAAW,EAAEQ,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YACzEG,QAAQ;YAAAnE,QAAA,gBAER1C,OAAA;cAAQ0G,KAAK,EAAC,EAAE;cAAAhE,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACxCmD,UAAU,CAAClC,GAAG,CAAC+C,GAAG,iBACjB9G,OAAA;cAA8B0G,KAAK,EAAEI,GAAG,CAACjB,WAAY;cAAAnD,QAAA,EAClDoE,GAAG,CAAC5C;YAAa,GADP4C,GAAG,CAACjB,WAAW;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEpB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5B9C,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEf,QAAQ,CAAC1B,UAAW;YAC3B0C,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAE1B,UAAU,EAAEoC,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YACxEM,WAAW,EAAC,sBAAsB;YAClCH,QAAQ;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnC9C,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEf,QAAQ,CAACxB,mBAAoB;YACpCwC,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAExB,mBAAmB,EAAEkC,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YACjFM,WAAW,EAAC,gBAAgB;YAC5BH,QAAQ;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpB9C,OAAA;YACE+G,IAAI,EAAC,QAAQ;YACbL,KAAK,EAAEf,QAAQ,CAACG,iBAAkB;YAClCa,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEG,iBAAiB,EAAEO,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YAC/EO,GAAG,EAAC,MAAM;YACVC,GAAG,EAAC;UAAM;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrB9C,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEf,QAAQ,CAACI,KAAM;YACtBY,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEI,KAAK,EAAEM,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YACnEM,WAAW,EAAC;UAAa;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChC9C,OAAA;YACE+G,IAAI,EAAC,QAAQ;YACbL,KAAK,EAAEf,QAAQ,CAACvB,gBAAiB;YACjCuC,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEvB,gBAAgB,EAAEiC,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YAC9EO,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRL,QAAQ;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1C,OAAA;YAAA0C,QAAA,EAAO;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChC9C,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEf,QAAQ,CAACK,gBAAiB;YACjCW,QAAQ,EAAGN,CAAC,IAAKT,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEK,gBAAgB,EAAEK,CAAC,CAACO,MAAM,CAACF;YAAK,CAAC,CAAE;YAC9EG,QAAQ;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1C,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAAC9D,OAAO,EAAEY,OAAQ;YAAAnB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvD9C,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAACI,QAAQ,EAAEvG,OAAQ;YAAA8B,QAAA,EACrC9B,OAAO,GAAG,WAAW,GAAG;UAAa;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4C,GAAA,CArJI9B,cAAc;AAAAwD,GAAA,GAAdxD,cAAc;AAuJpB,eAAe3D,eAAe;AAAC,IAAAuD,EAAA,EAAAgB,GAAA,EAAAa,GAAA,EAAAI,GAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAA7D,EAAA;AAAA6D,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}