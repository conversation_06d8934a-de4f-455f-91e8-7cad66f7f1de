{"ast": null, "code": "/**\n * Map utility functions for trip planning with Google Maps API\n * Clean and simple implementation for Siyoga Travel\n */\n\n// Sri Lanka coordinates\nexport const SRI_LANKA_CENTER = {\n  lat: 7.8731,\n  lng: 80.7718\n};\n\n// Google Maps API key (using GoMaps.pro for free tier)\nexport const GOOGLE_MAPS_API_KEY = \"AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLE\";\nexport const GOMAPS_API_KEY = GOOGLE_MAPS_API_KEY; // Alias for compatibility\n\n// Google Maps API URL with callback (matching Real Project format)\nexport const GOOGLE_MAPS_API_URL = `https://maps.gomaps.pro/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,drawing&callback=initAutocomplete`;\n\n// Popular Sri Lankan destinations with coordinates\nexport const SRI_LANKA_DESTINATIONS = {\n  'Colombo': {\n    lat: 6.9271,\n    lng: 79.8612\n  },\n  'Kandy': {\n    lat: 7.2906,\n    lng: 80.6337\n  },\n  'Galle': {\n    lat: 6.0535,\n    lng: 80.2210\n  },\n  'Sigiriya': {\n    lat: 7.9570,\n    lng: 80.7603\n  },\n  'Ella': {\n    lat: 6.8667,\n    lng: 81.0466\n  },\n  'Nuwara Eliya': {\n    lat: 6.9497,\n    lng: 80.7891\n  },\n  'Yala National Park': {\n    lat: 6.3698,\n    lng: 81.5046\n  },\n  'Anuradhapura': {\n    lat: 8.3114,\n    lng: 80.4037\n  },\n  'Jaffna': {\n    lat: 9.6615,\n    lng: 80.0255\n  },\n  'Trincomalee': {\n    lat: 8.5874,\n    lng: 81.2152\n  },\n  'Mirissa': {\n    lat: 5.9483,\n    lng: 80.4589\n  },\n  'Bentota': {\n    lat: 6.4213,\n    lng: 79.9959\n  },\n  'Negombo': {\n    lat: 7.2081,\n    lng: 79.8371\n  },\n  'Dambulla': {\n    lat: 7.8675,\n    lng: 80.6518\n  },\n  'Adams Peak': {\n    lat: 6.8096,\n    lng: 80.4994\n  }\n};\n\n/**\n * Calculate distance between multiple locations using Google Maps Distance Matrix API\n * @param {Array} locations - Array of location addresses [origin, dest1, dest2, ...]\n * @param {Object} options - Additional options\n * @returns {Promise} - Promise that resolves with distance details\n */\nexport const calculateRouteDistance = async (locations, options = {}) => {\n  try {\n    const {\n      isReturnTrip = false,\n      startTime = '09:00',\n      additionalStopTime = 3 // hours\n    } = options;\n    if (!locations || locations.length < 2) {\n      return {\n        success: false,\n        error: 'At least origin and one destination are required'\n      };\n    }\n\n    // Create route array\n    let routeLocations = [...locations];\n\n    // For return trips, add origin as final destination\n    if (isReturnTrip) {\n      routeLocations.push(locations[0]);\n    }\n    console.log('Calculating route for:', routeLocations);\n\n    // Calculate distances for each segment\n    const segments = [];\n    let totalDistance = 0;\n    let totalDuration = 0;\n    for (let i = 0; i < routeLocations.length - 1; i++) {\n      const origin = routeLocations[i];\n      const destination = routeLocations[i + 1];\n      const segmentResult = await calculateSegmentDistance(origin, destination);\n      if (!segmentResult.success) {\n        return segmentResult;\n      }\n      segments.push({\n        from: origin,\n        to: destination,\n        distance: segmentResult.distance,\n        duration: segmentResult.duration,\n        index: i + 1\n      });\n      totalDistance += segmentResult.distance.value;\n      totalDuration += segmentResult.duration.value;\n\n      // Add stop time for intermediate destinations\n      if (i < routeLocations.length - 2) {\n        totalDuration += additionalStopTime * 3600; // Convert hours to seconds\n      }\n    }\n\n    // Convert to readable format\n    const totalDistanceKm = Math.round(totalDistance / 1000);\n    const totalDurationHours = totalDuration / 3600;\n\n    // Calculate trip details\n    const tripDetails = calculateTripDetails(totalDurationHours, startTime);\n    return {\n      success: true,\n      totalDistance: totalDistanceKm,\n      totalDuration: totalDurationHours,\n      segments,\n      tripDetails,\n      breakdown: {\n        segments: segments.map(seg => ({\n          from: seg.from,\n          to: seg.to,\n          distance: `${Math.round(seg.distance.value / 1000)} km`,\n          duration: `${Math.round(seg.duration.value / 60)} mins`\n        })),\n        totalDistance: `${totalDistanceKm} km`,\n        totalDuration: formatDuration(totalDurationHours),\n        tripType: isReturnTrip ? 'Return Trip' : 'One-way Trip'\n      },\n      feasibility: {\n        tripType: isReturnTrip ? 'Return Trip' : 'One-way Trip',\n        distance: `${totalDistanceKm} km`,\n        drivingTime: formatDuration(totalDuration / 3600 - additionalStopTime * (segments.length - 1)),\n        stopTime: segments.length > 1 ? `${additionalStopTime * (segments.length - 1)} hours` : 'No stops',\n        totalDuration: formatDuration(totalDurationHours)\n      },\n      schedule: {\n        startTime,\n        estimatedEndTime: calculateEndTime(startTime, totalDurationHours),\n        daysNeeded: tripDetails.daysNeeded\n      }\n    };\n  } catch (error) {\n    console.error('Error calculating route distance:', error);\n    return {\n      success: false,\n      error: 'Failed to calculate route distance. Please try again.'\n    };\n  }\n};\n\n/**\n * Calculate distance between two locations using Google Maps Distance Matrix API\n * @param {string} origin - Origin address\n * @param {string} destination - Destination address\n * @returns {Promise} - Promise that resolves with distance details\n */\nconst calculateSegmentDistance = async (origin, destination) => {\n  try {\n    const url = `https://maps.gomaps.pro/maps/api/distancematrix/json?origins=${encodeURIComponent(origin)}&destinations=${encodeURIComponent(destination)}&mode=driving&key=${GOOGLE_MAPS_API_KEY}`;\n    console.log(`Calculating distance: ${origin} → ${destination}`);\n    const response = await fetch(url);\n    const data = await response.json();\n    if (data.status === 'OK' && data.rows[0].elements[0].status === 'OK') {\n      const element = data.rows[0].elements[0];\n      return {\n        success: true,\n        distance: element.distance,\n        duration: element.duration,\n        originAddress: data.origin_addresses[0],\n        destinationAddress: data.destination_addresses[0]\n      };\n    } else {\n      console.error('Distance Matrix API error:', data);\n      return {\n        success: false,\n        error: `Failed to calculate distance between ${origin} and ${destination}`\n      };\n    }\n  } catch (error) {\n    console.error('Error fetching distance:', error);\n    return {\n      success: false,\n      error: 'Network error while calculating distance'\n    };\n  }\n};\n\n/**\n * Calculate trip details based on duration\n * @param {number} totalHours - Total trip duration in hours\n * @param {string} startTime - Start time in HH:MM format\n * @returns {Object} - Trip details\n */\nconst calculateTripDetails = (totalHours, startTime) => {\n  const daysNeeded = totalHours > 12 ? Math.ceil(totalHours / 12) : 1;\n  const endTime = calculateEndTime(startTime, totalHours);\n  return {\n    daysNeeded,\n    endTime,\n    feasible: totalHours <= 24,\n    // Trips over 24 hours need special planning\n    recommendation: totalHours > 12 ? 'This trip will require overnight accommodation' : 'This trip can be completed in one day'\n  };\n};\n\n/**\n * Calculate end time based on start time and duration\n * @param {string} startTime - Start time in HH:MM format\n * @param {number} durationHours - Duration in hours\n * @returns {string} - End time in HH:MM format\n */\nconst calculateEndTime = (startTime, durationHours) => {\n  const [hours, minutes] = startTime.split(':').map(Number);\n  const startMinutes = hours * 60 + minutes;\n  const endMinutes = startMinutes + durationHours * 60;\n  const endHours = Math.floor(endMinutes / 60) % 24;\n  const endMins = Math.round(endMinutes % 60);\n  return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;\n};\n\n/**\n * Format duration from hours to readable string\n * @param {number} hours - Duration in hours\n * @returns {string} - Formatted duration\n */\nconst formatDuration = hours => {\n  const wholeHours = Math.floor(hours);\n  const minutes = Math.round((hours - wholeHours) * 60);\n  if (wholeHours > 0 && minutes > 0) {\n    return `${wholeHours} hours ${minutes} mins`;\n  } else if (wholeHours > 0) {\n    return `${wholeHours} hours`;\n  } else {\n    return `${minutes} mins`;\n  }\n};\n\n/**\n * Load Google Maps API script with callback approach (matching Real Project)\n * @returns {Promise} - Promise that resolves when API is loaded\n */\nexport const loadGoogleMapsAPI = () => {\n  return new Promise((resolve, reject) => {\n    if (window.google && window.google.maps && window.google.maps.places) {\n      resolve();\n      return;\n    }\n\n    // Check if script is already loading\n    const existingScript = document.querySelector(`script[src*=\"maps.gomaps.pro\"]`);\n    if (existingScript) {\n      const checkGoogleMaps = setInterval(() => {\n        if (window.google && window.google.maps && window.google.maps.places) {\n          clearInterval(checkGoogleMaps);\n          resolve();\n        }\n      }, 100);\n      setTimeout(() => {\n        clearInterval(checkGoogleMaps);\n        reject(new Error('Timeout waiting for Google Maps API'));\n      }, 10000);\n      return;\n    }\n\n    // Set up global callback\n    const originalInitAutocomplete = window.initAutocomplete;\n    window.initAutocomplete = () => {\n      console.log(\"Google Maps API loaded via callback\");\n      resolve();\n\n      // Call original callback if it exists\n      if (originalInitAutocomplete && typeof originalInitAutocomplete === 'function') {\n        originalInitAutocomplete();\n      }\n    };\n    const script = document.createElement('script');\n    script.src = GOOGLE_MAPS_API_URL;\n    script.async = true;\n    script.defer = true;\n    script.onerror = () => {\n      reject(new Error('Failed to load Google Maps API script'));\n    };\n    document.head.appendChild(script);\n  });\n};\nexport default {\n  calculateRouteDistance,\n  loadGoogleMapsAPI,\n  SRI_LANKA_CENTER,\n  SRI_LANKA_DESTINATIONS\n};", "map": {"version": 3, "names": ["SRI_LANKA_CENTER", "lat", "lng", "GOOGLE_MAPS_API_KEY", "GOMAPS_API_KEY", "GOOGLE_MAPS_API_URL", "SRI_LANKA_DESTINATIONS", "calculateRouteDistance", "locations", "options", "isReturnTrip", "startTime", "additionalStopTime", "length", "success", "error", "routeLocations", "push", "console", "log", "segments", "totalDistance", "totalDuration", "i", "origin", "destination", "segmentResult", "calculateSegmentDistance", "from", "to", "distance", "duration", "index", "value", "totalDistanceKm", "Math", "round", "totalDurationHours", "tripDetails", "calculateTripDetails", "breakdown", "map", "seg", "formatDuration", "tripType", "feasibility", "drivingTime", "stopTime", "schedule", "estimatedEndTime", "calculateEndTime", "daysNeeded", "url", "encodeURIComponent", "response", "fetch", "data", "json", "status", "rows", "elements", "element", "origin<PERSON><PERSON><PERSON>", "origin_addresses", "destinationAddress", "destination_addresses", "totalHours", "ceil", "endTime", "feasible", "recommendation", "durationHours", "hours", "minutes", "split", "Number", "startMinutes", "endMinutes", "endHours", "floor", "endMins", "toString", "padStart", "wholeHours", "loadGoogleMapsAPI", "Promise", "resolve", "reject", "window", "google", "maps", "places", "existingScript", "document", "querySelector", "checkGoogleMaps", "setInterval", "clearInterval", "setTimeout", "Error", "originalInitAutocomplete", "initAutocomplete", "script", "createElement", "src", "async", "defer", "onerror", "head", "append<PERSON><PERSON><PERSON>"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/utils/mapUtils.js"], "sourcesContent": ["/**\n * Map utility functions for trip planning with Google Maps API\n * Clean and simple implementation for Siyoga Travel\n */\n\n// Sri Lanka coordinates\nexport const SRI_LANKA_CENTER = { lat: 7.8731, lng: 80.7718 };\n\n// Google Maps API key (using GoMaps.pro for free tier)\nexport const GOOGLE_MAPS_API_KEY = \"AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLE\";\nexport const GOMAPS_API_KEY = GOOGLE_MAPS_API_KEY; // Alias for compatibility\n\n// Google Maps API URL with callback (matching Real Project format)\nexport const GOOGLE_MAPS_API_URL = `https://maps.gomaps.pro/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,drawing&callback=initAutocomplete`;\n\n// Popular Sri Lankan destinations with coordinates\nexport const SRI_LANKA_DESTINATIONS = {\n  'Colombo': { lat: 6.9271, lng: 79.8612 },\n  'Kandy': { lat: 7.2906, lng: 80.6337 },\n  'Galle': { lat: 6.0535, lng: 80.2210 },\n  'Sigiriya': { lat: 7.9570, lng: 80.7603 },\n  'Ella': { lat: 6.8667, lng: 81.0466 },\n  'Nuwara Eliya': { lat: 6.9497, lng: 80.7891 },\n  'Yala National Park': { lat: 6.3698, lng: 81.5046 },\n  'Anuradhapura': { lat: 8.3114, lng: 80.4037 },\n  'Jaffna': { lat: 9.6615, lng: 80.0255 },\n  'Trincomalee': { lat: 8.5874, lng: 81.2152 },\n  'Mirissa': { lat: 5.9483, lng: 80.4589 },\n  'Bentota': { lat: 6.4213, lng: 79.9959 },\n  'Negombo': { lat: 7.2081, lng: 79.8371 },\n  'Dambulla': { lat: 7.8675, lng: 80.6518 },\n  'Adams Peak': { lat: 6.8096, lng: 80.4994 }\n};\n\n/**\n * Calculate distance between multiple locations using Google Maps Distance Matrix API\n * @param {Array} locations - Array of location addresses [origin, dest1, dest2, ...]\n * @param {Object} options - Additional options\n * @returns {Promise} - Promise that resolves with distance details\n */\nexport const calculateRouteDistance = async (locations, options = {}) => {\n  try {\n    const {\n      isReturnTrip = false,\n      startTime = '09:00',\n      additionalStopTime = 3 // hours\n    } = options;\n\n    if (!locations || locations.length < 2) {\n      return {\n        success: false,\n        error: 'At least origin and one destination are required'\n      };\n    }\n\n    // Create route array\n    let routeLocations = [...locations];\n    \n    // For return trips, add origin as final destination\n    if (isReturnTrip) {\n      routeLocations.push(locations[0]);\n    }\n\n    console.log('Calculating route for:', routeLocations);\n\n    // Calculate distances for each segment\n    const segments = [];\n    let totalDistance = 0;\n    let totalDuration = 0;\n\n    for (let i = 0; i < routeLocations.length - 1; i++) {\n      const origin = routeLocations[i];\n      const destination = routeLocations[i + 1];\n      \n      const segmentResult = await calculateSegmentDistance(origin, destination);\n      \n      if (!segmentResult.success) {\n        return segmentResult;\n      }\n      \n      segments.push({\n        from: origin,\n        to: destination,\n        distance: segmentResult.distance,\n        duration: segmentResult.duration,\n        index: i + 1\n      });\n      \n      totalDistance += segmentResult.distance.value;\n      totalDuration += segmentResult.duration.value;\n      \n      // Add stop time for intermediate destinations\n      if (i < routeLocations.length - 2) {\n        totalDuration += additionalStopTime * 3600; // Convert hours to seconds\n      }\n    }\n\n    // Convert to readable format\n    const totalDistanceKm = Math.round(totalDistance / 1000);\n    const totalDurationHours = totalDuration / 3600;\n    \n    // Calculate trip details\n    const tripDetails = calculateTripDetails(totalDurationHours, startTime);\n    \n    return {\n      success: true,\n      totalDistance: totalDistanceKm,\n      totalDuration: totalDurationHours,\n      segments,\n      tripDetails,\n      breakdown: {\n        segments: segments.map(seg => ({\n          from: seg.from,\n          to: seg.to,\n          distance: `${Math.round(seg.distance.value / 1000)} km`,\n          duration: `${Math.round(seg.duration.value / 60)} mins`\n        })),\n        totalDistance: `${totalDistanceKm} km`,\n        totalDuration: formatDuration(totalDurationHours),\n        tripType: isReturnTrip ? 'Return Trip' : 'One-way Trip'\n      },\n      feasibility: {\n        tripType: isReturnTrip ? 'Return Trip' : 'One-way Trip',\n        distance: `${totalDistanceKm} km`,\n        drivingTime: formatDuration(totalDuration / 3600 - additionalStopTime * (segments.length - 1)),\n        stopTime: segments.length > 1 ? `${additionalStopTime * (segments.length - 1)} hours` : 'No stops',\n        totalDuration: formatDuration(totalDurationHours)\n      },\n      schedule: {\n        startTime,\n        estimatedEndTime: calculateEndTime(startTime, totalDurationHours),\n        daysNeeded: tripDetails.daysNeeded\n      }\n    };\n\n  } catch (error) {\n    console.error('Error calculating route distance:', error);\n    return {\n      success: false,\n      error: 'Failed to calculate route distance. Please try again.'\n    };\n  }\n};\n\n/**\n * Calculate distance between two locations using Google Maps Distance Matrix API\n * @param {string} origin - Origin address\n * @param {string} destination - Destination address\n * @returns {Promise} - Promise that resolves with distance details\n */\nconst calculateSegmentDistance = async (origin, destination) => {\n  try {\n    const url = `https://maps.gomaps.pro/maps/api/distancematrix/json?origins=${encodeURIComponent(origin)}&destinations=${encodeURIComponent(destination)}&mode=driving&key=${GOOGLE_MAPS_API_KEY}`;\n    \n    console.log(`Calculating distance: ${origin} → ${destination}`);\n    \n    const response = await fetch(url);\n    const data = await response.json();\n    \n    if (data.status === 'OK' && data.rows[0].elements[0].status === 'OK') {\n      const element = data.rows[0].elements[0];\n      \n      return {\n        success: true,\n        distance: element.distance,\n        duration: element.duration,\n        originAddress: data.origin_addresses[0],\n        destinationAddress: data.destination_addresses[0]\n      };\n    } else {\n      console.error('Distance Matrix API error:', data);\n      return {\n        success: false,\n        error: `Failed to calculate distance between ${origin} and ${destination}`\n      };\n    }\n  } catch (error) {\n    console.error('Error fetching distance:', error);\n    return {\n      success: false,\n      error: 'Network error while calculating distance'\n    };\n  }\n};\n\n/**\n * Calculate trip details based on duration\n * @param {number} totalHours - Total trip duration in hours\n * @param {string} startTime - Start time in HH:MM format\n * @returns {Object} - Trip details\n */\nconst calculateTripDetails = (totalHours, startTime) => {\n  const daysNeeded = totalHours > 12 ? Math.ceil(totalHours / 12) : 1;\n  const endTime = calculateEndTime(startTime, totalHours);\n  \n  return {\n    daysNeeded,\n    endTime,\n    feasible: totalHours <= 24, // Trips over 24 hours need special planning\n    recommendation: totalHours > 12 ? \n      'This trip will require overnight accommodation' : \n      'This trip can be completed in one day'\n  };\n};\n\n/**\n * Calculate end time based on start time and duration\n * @param {string} startTime - Start time in HH:MM format\n * @param {number} durationHours - Duration in hours\n * @returns {string} - End time in HH:MM format\n */\nconst calculateEndTime = (startTime, durationHours) => {\n  const [hours, minutes] = startTime.split(':').map(Number);\n  const startMinutes = hours * 60 + minutes;\n  const endMinutes = startMinutes + (durationHours * 60);\n  \n  const endHours = Math.floor(endMinutes / 60) % 24;\n  const endMins = Math.round(endMinutes % 60);\n  \n  return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;\n};\n\n/**\n * Format duration from hours to readable string\n * @param {number} hours - Duration in hours\n * @returns {string} - Formatted duration\n */\nconst formatDuration = (hours) => {\n  const wholeHours = Math.floor(hours);\n  const minutes = Math.round((hours - wholeHours) * 60);\n  \n  if (wholeHours > 0 && minutes > 0) {\n    return `${wholeHours} hours ${minutes} mins`;\n  } else if (wholeHours > 0) {\n    return `${wholeHours} hours`;\n  } else {\n    return `${minutes} mins`;\n  }\n};\n\n/**\n * Load Google Maps API script with callback approach (matching Real Project)\n * @returns {Promise} - Promise that resolves when API is loaded\n */\nexport const loadGoogleMapsAPI = () => {\n  return new Promise((resolve, reject) => {\n    if (window.google && window.google.maps && window.google.maps.places) {\n      resolve();\n      return;\n    }\n\n    // Check if script is already loading\n    const existingScript = document.querySelector(`script[src*=\"maps.gomaps.pro\"]`);\n    if (existingScript) {\n      const checkGoogleMaps = setInterval(() => {\n        if (window.google && window.google.maps && window.google.maps.places) {\n          clearInterval(checkGoogleMaps);\n          resolve();\n        }\n      }, 100);\n\n      setTimeout(() => {\n        clearInterval(checkGoogleMaps);\n        reject(new Error('Timeout waiting for Google Maps API'));\n      }, 10000);\n\n      return;\n    }\n\n    // Set up global callback\n    const originalInitAutocomplete = window.initAutocomplete;\n    window.initAutocomplete = () => {\n      console.log(\"Google Maps API loaded via callback\");\n      resolve();\n\n      // Call original callback if it exists\n      if (originalInitAutocomplete && typeof originalInitAutocomplete === 'function') {\n        originalInitAutocomplete();\n      }\n    };\n\n    const script = document.createElement('script');\n    script.src = GOOGLE_MAPS_API_URL;\n    script.async = true;\n    script.defer = true;\n\n    script.onerror = () => {\n      reject(new Error('Failed to load Google Maps API script'));\n    };\n\n    document.head.appendChild(script);\n  });\n};\n\nexport default {\n  calculateRouteDistance,\n  loadGoogleMapsAPI,\n  SRI_LANKA_CENTER,\n  SRI_LANKA_DESTINATIONS\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,gBAAgB,GAAG;EAAEC,GAAG,EAAE,MAAM;EAAEC,GAAG,EAAE;AAAQ,CAAC;;AAE7D;AACA,OAAO,MAAMC,mBAAmB,GAAG,yCAAyC;AAC5E,OAAO,MAAMC,cAAc,GAAGD,mBAAmB,CAAC,CAAC;;AAEnD;AACA,OAAO,MAAME,mBAAmB,GAAG,2CAA2CF,mBAAmB,qDAAqD;;AAEtJ;AACA,OAAO,MAAMG,sBAAsB,GAAG;EACpC,SAAS,EAAE;IAAEL,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxC,OAAO,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACtC,OAAO,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACtC,UAAU,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACzC,MAAM,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACrC,cAAc,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAC7C,oBAAoB,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACnD,cAAc,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAC7C,QAAQ,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACvC,aAAa,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAC5C,SAAS,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxC,SAAS,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxC,SAAS,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxC,UAAU,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACzC,YAAY,EAAE;IAAED,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAQ;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,sBAAsB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACvE,IAAI;IACF,MAAM;MACJC,YAAY,GAAG,KAAK;MACpBC,SAAS,GAAG,OAAO;MACnBC,kBAAkB,GAAG,CAAC,CAAC;IACzB,CAAC,GAAGH,OAAO;IAEX,IAAI,CAACD,SAAS,IAAIA,SAAS,CAACK,MAAM,GAAG,CAAC,EAAE;MACtC,OAAO;QACLC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,IAAIC,cAAc,GAAG,CAAC,GAAGR,SAAS,CAAC;;IAEnC;IACA,IAAIE,YAAY,EAAE;MAChBM,cAAc,CAACC,IAAI,CAACT,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC;IAEAU,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,cAAc,CAAC;;IAErD;IACA,MAAMI,QAAQ,GAAG,EAAE;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,aAAa,GAAG,CAAC;IAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,cAAc,CAACH,MAAM,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;MAClD,MAAMC,MAAM,GAAGR,cAAc,CAACO,CAAC,CAAC;MAChC,MAAME,WAAW,GAAGT,cAAc,CAACO,CAAC,GAAG,CAAC,CAAC;MAEzC,MAAMG,aAAa,GAAG,MAAMC,wBAAwB,CAACH,MAAM,EAAEC,WAAW,CAAC;MAEzE,IAAI,CAACC,aAAa,CAACZ,OAAO,EAAE;QAC1B,OAAOY,aAAa;MACtB;MAEAN,QAAQ,CAACH,IAAI,CAAC;QACZW,IAAI,EAAEJ,MAAM;QACZK,EAAE,EAAEJ,WAAW;QACfK,QAAQ,EAAEJ,aAAa,CAACI,QAAQ;QAChCC,QAAQ,EAAEL,aAAa,CAACK,QAAQ;QAChCC,KAAK,EAAET,CAAC,GAAG;MACb,CAAC,CAAC;MAEFF,aAAa,IAAIK,aAAa,CAACI,QAAQ,CAACG,KAAK;MAC7CX,aAAa,IAAII,aAAa,CAACK,QAAQ,CAACE,KAAK;;MAE7C;MACA,IAAIV,CAAC,GAAGP,cAAc,CAACH,MAAM,GAAG,CAAC,EAAE;QACjCS,aAAa,IAAIV,kBAAkB,GAAG,IAAI,CAAC,CAAC;MAC9C;IACF;;IAEA;IACA,MAAMsB,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACf,aAAa,GAAG,IAAI,CAAC;IACxD,MAAMgB,kBAAkB,GAAGf,aAAa,GAAG,IAAI;;IAE/C;IACA,MAAMgB,WAAW,GAAGC,oBAAoB,CAACF,kBAAkB,EAAE1B,SAAS,CAAC;IAEvE,OAAO;MACLG,OAAO,EAAE,IAAI;MACbO,aAAa,EAAEa,eAAe;MAC9BZ,aAAa,EAAEe,kBAAkB;MACjCjB,QAAQ;MACRkB,WAAW;MACXE,SAAS,EAAE;QACTpB,QAAQ,EAAEA,QAAQ,CAACqB,GAAG,CAACC,GAAG,KAAK;UAC7Bd,IAAI,EAAEc,GAAG,CAACd,IAAI;UACdC,EAAE,EAAEa,GAAG,CAACb,EAAE;UACVC,QAAQ,EAAE,GAAGK,IAAI,CAACC,KAAK,CAACM,GAAG,CAACZ,QAAQ,CAACG,KAAK,GAAG,IAAI,CAAC,KAAK;UACvDF,QAAQ,EAAE,GAAGI,IAAI,CAACC,KAAK,CAACM,GAAG,CAACX,QAAQ,CAACE,KAAK,GAAG,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QACHZ,aAAa,EAAE,GAAGa,eAAe,KAAK;QACtCZ,aAAa,EAAEqB,cAAc,CAACN,kBAAkB,CAAC;QACjDO,QAAQ,EAAElC,YAAY,GAAG,aAAa,GAAG;MAC3C,CAAC;MACDmC,WAAW,EAAE;QACXD,QAAQ,EAAElC,YAAY,GAAG,aAAa,GAAG,cAAc;QACvDoB,QAAQ,EAAE,GAAGI,eAAe,KAAK;QACjCY,WAAW,EAAEH,cAAc,CAACrB,aAAa,GAAG,IAAI,GAAGV,kBAAkB,IAAIQ,QAAQ,CAACP,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9FkC,QAAQ,EAAE3B,QAAQ,CAACP,MAAM,GAAG,CAAC,GAAG,GAAGD,kBAAkB,IAAIQ,QAAQ,CAACP,MAAM,GAAG,CAAC,CAAC,QAAQ,GAAG,UAAU;QAClGS,aAAa,EAAEqB,cAAc,CAACN,kBAAkB;MAClD,CAAC;MACDW,QAAQ,EAAE;QACRrC,SAAS;QACTsC,gBAAgB,EAAEC,gBAAgB,CAACvC,SAAS,EAAE0B,kBAAkB,CAAC;QACjEc,UAAU,EAAEb,WAAW,CAACa;MAC1B;IACF,CAAC;EAEH,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACdG,OAAO,CAACH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO;MACLD,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,wBAAwB,GAAG,MAAAA,CAAOH,MAAM,EAAEC,WAAW,KAAK;EAC9D,IAAI;IACF,MAAM2B,GAAG,GAAG,gEAAgEC,kBAAkB,CAAC7B,MAAM,CAAC,iBAAiB6B,kBAAkB,CAAC5B,WAAW,CAAC,qBAAqBtB,mBAAmB,EAAE;IAEhMe,OAAO,CAACC,GAAG,CAAC,yBAAyBK,MAAM,MAAMC,WAAW,EAAE,CAAC;IAE/D,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,CAAC;IACjC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,CAACE,MAAM,KAAK,IAAI,IAAIF,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACF,MAAM,KAAK,IAAI,EAAE;MACpE,MAAMG,OAAO,GAAGL,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAExC,OAAO;QACL9C,OAAO,EAAE,IAAI;QACbgB,QAAQ,EAAE+B,OAAO,CAAC/B,QAAQ;QAC1BC,QAAQ,EAAE8B,OAAO,CAAC9B,QAAQ;QAC1B+B,aAAa,EAAEN,IAAI,CAACO,gBAAgB,CAAC,CAAC,CAAC;QACvCC,kBAAkB,EAAER,IAAI,CAACS,qBAAqB,CAAC,CAAC;MAClD,CAAC;IACH,CAAC,MAAM;MACL/C,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAEyC,IAAI,CAAC;MACjD,OAAO;QACL1C,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,wCAAwCS,MAAM,QAAQC,WAAW;MAC1E,CAAC;IACH;EACF,CAAC,CAAC,OAAOV,KAAK,EAAE;IACdG,OAAO,CAACH,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO;MACLD,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,oBAAoB,GAAGA,CAAC2B,UAAU,EAAEvD,SAAS,KAAK;EACtD,MAAMwC,UAAU,GAAGe,UAAU,GAAG,EAAE,GAAG/B,IAAI,CAACgC,IAAI,CAACD,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC;EACnE,MAAME,OAAO,GAAGlB,gBAAgB,CAACvC,SAAS,EAAEuD,UAAU,CAAC;EAEvD,OAAO;IACLf,UAAU;IACViB,OAAO;IACPC,QAAQ,EAAEH,UAAU,IAAI,EAAE;IAAE;IAC5BI,cAAc,EAAEJ,UAAU,GAAG,EAAE,GAC7B,gDAAgD,GAChD;EACJ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhB,gBAAgB,GAAGA,CAACvC,SAAS,EAAE4D,aAAa,KAAK;EACrD,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAG9D,SAAS,CAAC+D,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACkC,MAAM,CAAC;EACzD,MAAMC,YAAY,GAAGJ,KAAK,GAAG,EAAE,GAAGC,OAAO;EACzC,MAAMI,UAAU,GAAGD,YAAY,GAAIL,aAAa,GAAG,EAAG;EAEtD,MAAMO,QAAQ,GAAG3C,IAAI,CAAC4C,KAAK,CAACF,UAAU,GAAG,EAAE,CAAC,GAAG,EAAE;EACjD,MAAMG,OAAO,GAAG7C,IAAI,CAACC,KAAK,CAACyC,UAAU,GAAG,EAAE,CAAC;EAE3C,OAAO,GAAGC,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;AACzF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMvC,cAAc,GAAI6B,KAAK,IAAK;EAChC,MAAMW,UAAU,GAAGhD,IAAI,CAAC4C,KAAK,CAACP,KAAK,CAAC;EACpC,MAAMC,OAAO,GAAGtC,IAAI,CAACC,KAAK,CAAC,CAACoC,KAAK,GAAGW,UAAU,IAAI,EAAE,CAAC;EAErD,IAAIA,UAAU,GAAG,CAAC,IAAIV,OAAO,GAAG,CAAC,EAAE;IACjC,OAAO,GAAGU,UAAU,UAAUV,OAAO,OAAO;EAC9C,CAAC,MAAM,IAAIU,UAAU,GAAG,CAAC,EAAE;IACzB,OAAO,GAAGA,UAAU,QAAQ;EAC9B,CAAC,MAAM;IACL,OAAO,GAAGV,OAAO,OAAO;EAC1B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;EACrC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAIC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIF,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE;MACpEL,OAAO,CAAC,CAAC;MACT;IACF;;IAEA;IACA,MAAMM,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,gCAAgC,CAAC;IAC/E,IAAIF,cAAc,EAAE;MAClB,MAAMG,eAAe,GAAGC,WAAW,CAAC,MAAM;QACxC,IAAIR,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIF,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE;UACpEM,aAAa,CAACF,eAAe,CAAC;UAC9BT,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,GAAG,CAAC;MAEPY,UAAU,CAAC,MAAM;QACfD,aAAa,CAACF,eAAe,CAAC;QAC9BR,MAAM,CAAC,IAAIY,KAAK,CAAC,qCAAqC,CAAC,CAAC;MAC1D,CAAC,EAAE,KAAK,CAAC;MAET;IACF;;IAEA;IACA,MAAMC,wBAAwB,GAAGZ,MAAM,CAACa,gBAAgB;IACxDb,MAAM,CAACa,gBAAgB,GAAG,MAAM;MAC9BnF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDmE,OAAO,CAAC,CAAC;;MAET;MACA,IAAIc,wBAAwB,IAAI,OAAOA,wBAAwB,KAAK,UAAU,EAAE;QAC9EA,wBAAwB,CAAC,CAAC;MAC5B;IACF,CAAC;IAED,MAAME,MAAM,GAAGT,QAAQ,CAACU,aAAa,CAAC,QAAQ,CAAC;IAC/CD,MAAM,CAACE,GAAG,GAAGnG,mBAAmB;IAChCiG,MAAM,CAACG,KAAK,GAAG,IAAI;IACnBH,MAAM,CAACI,KAAK,GAAG,IAAI;IAEnBJ,MAAM,CAACK,OAAO,GAAG,MAAM;MACrBpB,MAAM,CAAC,IAAIY,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC5D,CAAC;IAEDN,QAAQ,CAACe,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;EACnC,CAAC,CAAC;AACJ,CAAC;AAED,eAAe;EACb/F,sBAAsB;EACtB6E,iBAAiB;EACjBpF,gBAAgB;EAChBM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}