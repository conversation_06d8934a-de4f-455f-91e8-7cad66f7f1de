{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      navigate('/dashboard');\n    } else {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"auth-title\",\n      children: \"\\uD83C\\uDF0D Siyoga Travel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '2rem',\n        color: '#666'\n      },\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          name: \"email\",\n          value: formData.email,\n          onChange: handleChange,\n          required: true,\n          placeholder: \"Enter your email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          id: \"password\",\n          name: \"password\",\n          value: formData.password,\n          onChange: handleChange,\n          required: true,\n          placeholder: \"Enter your password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"btn\",\n        disabled: loading,\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-link\",\n      children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        children: \"Register here\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"kZib7MjH48sgWs40zHxjP3+nDeA=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "error", "setError", "loading", "setLoading", "login", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "marginBottom", "color", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nfunction Login() {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    const result = await login(formData.email, formData.password);\n    \n    if (result.success) {\n      navigate('/dashboard');\n    } else {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <h2 className=\"auth-title\">🌍 Siyoga Travel</h2>\n      <h3 style={{ textAlign: 'center', marginBottom: '2rem', color: '#666' }}>Login</h3>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"email\">Email</label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            required\n            placeholder=\"Enter your email\"\n          />\n        </div>\n        \n        <div className=\"form-group\">\n          <label htmlFor=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            required\n            placeholder=\"Enter your password\"\n          />\n        </div>\n        \n        <button type=\"submit\" className=\"btn\" disabled={loading}>\n          {loading ? 'Logging in...' : 'Login'}\n        </button>\n      </form>\n      \n      <div className=\"auth-link\">\n        Don't have an account? <Link to=\"/register\">Register here</Link>\n      </div>\n    </div>\n  );\n}\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEgB;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3B,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BV,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACW,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBX,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMU,MAAM,GAAG,MAAMT,KAAK,CAACR,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAE7D,IAAIc,MAAM,CAACC,OAAO,EAAE;MAClBT,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,MAAM;MACLJ,QAAQ,CAACY,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAZ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKuB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BxB,OAAA;MAAIuB,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChD5B,OAAA;MAAI6B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAR,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAElFrB,KAAK,iBAAIP,OAAA;MAAKuB,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEjB;IAAK;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9C5B,OAAA;MAAMiC,QAAQ,EAAEf,YAAa;MAAAM,QAAA,gBAC3BxB,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAOkC,OAAO,EAAC,OAAO;UAAAV,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpC5B,OAAA;UACEmC,IAAI,EAAC,OAAO;UACZC,EAAE,EAAC,OAAO;UACVpB,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEd,QAAQ,CAACE,KAAM;UACtBgC,QAAQ,EAAExB,YAAa;UACvByB,QAAQ;UACRC,WAAW,EAAC;QAAkB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAOkC,OAAO,EAAC,UAAU;UAAAV,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C5B,OAAA;UACEmC,IAAI,EAAC,UAAU;UACfC,EAAE,EAAC,UAAU;UACbpB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEd,QAAQ,CAACG,QAAS;UACzB+B,QAAQ,EAAExB,YAAa;UACvByB,QAAQ;UACRC,WAAW,EAAC;QAAqB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5B,OAAA;QAAQmC,IAAI,EAAC,QAAQ;QAACZ,SAAS,EAAC,KAAK;QAACiB,QAAQ,EAAE/B,OAAQ;QAAAe,QAAA,EACrDf,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEP5B,OAAA;MAAKuB,SAAS,EAAC,WAAW;MAAAC,QAAA,GAAC,yBACF,eAAAxB,OAAA,CAACJ,IAAI;QAAC6C,EAAE,EAAC,WAAW;QAAAjB,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CA9EQD,KAAK;EAAA,QAQMH,OAAO,EACRD,WAAW;AAAA;AAAA6C,EAAA,GATrBzC,KAAK;AAgFd,eAAeA,KAAK;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}